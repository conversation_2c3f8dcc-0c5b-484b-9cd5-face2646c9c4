import React from "react";
import GlowingOrbsBackground from "./GlowingOrbsBackground";

const techs = [
  { name: "Node.js", color: "#66D9EF" },
  { name: "TypeScript", color: "#AE67FA" },
  { name: "PostgreS<PERSON>", color: "#FB8FBF" },
  { name: "<PERSON>er", color: "#66D9EF" },
];

export default function MinimalistPortfolio() {
  return (
    <div className="relative min-h-screen flex flex-col items-center justify-center px-4">
      <GlowingOrbsBackground />
      <div className="backdrop-blur-lg bg-white/5 border border-white/10 rounded-3xl shadow-2xl p-12 max-w-2xl w-full flex flex-col items-center">
        <div className="mb-6">
          <span className="px-4 py-1 rounded-full text-sm font-semibold tracking-wide text-[#F5F5F5] bg-[#AE67FA22] border border-[#AE67FA55] shadow-[0_0_16px_2px_#AE67FA55] animate-pulse">
            Ready to Innovate
          </span>
        </div>
        <h1
          className="text-5xl md:text-6xl font-extrabold text-[#F5F5F5] text-center mb-6"
          style={{
            textShadow: "0 0 16px #AE67FA, 0 0 32px #18122B, 0 1px 0 #AE67FA99",
            letterSpacing: "0.04em",
          }}
        >
          Backend Developer
        </h1>
        <div className="flex flex-wrap gap-4 justify-center mb-8">
          {techs.map((tech) => (
            <button
              key={tech.name}
              className="px-5 py-2 rounded-lg font-medium text-[#F5F5F5] border-2"
              style={{
                borderColor: tech.color,
                boxShadow: `0 0 12px 2px ${tech.color}55`,
                background: "#18122B99",
                transition: "box-shadow 0.2s, border-color 0.2s",
              }}
            >
              {tech.name}
            </button>
          ))}
        </div>
        <div className="w-full mt-4 p-6 rounded-2xl bg-white/10 border border-white/10 shadow-lg backdrop-blur-md text-[#F5F5F5] text-lg text-center">
          <p>
            I build robust, scalable backend systems with a focus on clean architecture, performance, and developer experience. Let’s create something extraordinary together.
          </p>
        </div>
      </div>
    </div>
  );
} 