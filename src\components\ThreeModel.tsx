
'use client';

import React, { Suspense, useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF, OrbitControls, Environment, ContactShadows } from '@react-three/drei';
import { EffectComposer, Bloom, ToneMapping } from '@react-three/postprocessing';
import { motion } from 'framer-motion';
import * as THREE from 'three';

// 3D Model Component
const CoolManModel = ({ ...props }) => {
  const groupRef = useRef<THREE.Group>(null);

  try {
    const { scene } = useGLTF('/models/cool man.glb');

    // Auto-rotate the model
    useFrame((state) => {
      if (groupRef.current) {
        groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.3;
      }
    });

    return (
      <group ref={groupRef} {...props}>
        <primitive object={scene} scale={[2, 2, 2]} position={[0, -1, 0]} />
      </group>
    );
  } catch (error) {
    console.error('Error loading 3D model:', error);
    return (
      <group ref={groupRef} {...props}>
        <mesh>
          <boxGeometry args={[1, 2, 1]} />
          <meshStandardMaterial color="#8b5cf6" />
        </mesh>
      </group>
    );
  }
};

// Enhanced Software Scene 3D Component - Matching Target Design
const CustomSoftwareScene = ({ ...props }) => {
  const groupRef = useRef<THREE.Group>(null);
  const monitorRef = useRef<THREE.Group>(null);
  const phoneRef = useRef<THREE.Group>(null);
  const cloudRef = useRef<THREE.Group>(null);
  const gear1Ref = useRef<THREE.Group>(null);
  const gear2Ref = useRef<THREE.Group>(null);
  const gear3Ref = useRef<THREE.Group>(null);
  const gear4Ref = useRef<THREE.Group>(null);

  // Create gear shape with teeth
  const createGearGeometry = (radius: number, teeth: number, depth: number) => {
    const shape = new THREE.Shape();
    const outerRadius = radius;
    const innerRadius = radius * 0.6;
    const toothHeight = radius * 0.2;

    for (let i = 0; i < teeth; i++) {
      const angle1 = (i / teeth) * Math.PI * 2;
      const angle2 = ((i + 0.5) / teeth) * Math.PI * 2;
      const angle3 = ((i + 1) / teeth) * Math.PI * 2;

      if (i === 0) {
        shape.moveTo(
          Math.cos(angle1) * outerRadius,
          Math.sin(angle1) * outerRadius
        );
      }

      shape.lineTo(
        Math.cos(angle1) * (outerRadius + toothHeight),
        Math.sin(angle1) * (outerRadius + toothHeight)
      );
      shape.lineTo(
        Math.cos(angle2) * (outerRadius + toothHeight),
        Math.sin(angle2) * (outerRadius + toothHeight)
      );
      shape.lineTo(
        Math.cos(angle3) * outerRadius,
        Math.sin(angle3) * outerRadius
      );
    }

    // Add inner circle
    const hole = new THREE.Path();
    hole.absarc(0, 0, innerRadius, 0, Math.PI * 2, true);
    shape.holes.push(hole);

    const extrudeSettings = {
      depth: depth,
      bevelEnabled: true,
      bevelSegments: 2,
      steps: 2,
      bevelSize: 0.02,
      bevelThickness: 0.02
    };

    return new THREE.ExtrudeGeometry(shape, extrudeSettings);
  };

  useFrame((state) => {
    const time = state.clock.elapsedTime;

    if (groupRef.current) {
      groupRef.current.position.y = Math.sin(time * 0.3) * 0.05;
    }

    // Rotate gears at different speeds
    if (gear1Ref.current) gear1Ref.current.rotation.z = time * 0.4;
    if (gear2Ref.current) gear2Ref.current.rotation.z = -time * 0.6;
    if (gear3Ref.current) gear3Ref.current.rotation.z = time * 0.5;
    if (gear4Ref.current) gear4Ref.current.rotation.z = -time * 0.3;

    // Gentle floating animations
    if (phoneRef.current) {
      phoneRef.current.position.y = Math.sin(time * 0.7) * 0.08 + 0.3;
      phoneRef.current.rotation.y = Math.sin(time * 0.2) * 0.05;
    }

    if (cloudRef.current) {
      cloudRef.current.position.y = Math.sin(time * 0.5) * 0.06 - 0.6;
    }
  });

  return (
    <group ref={groupRef} {...props}>
      {/* Circular Platform - More detailed */}
      <mesh position={[0, -1.5, 0]}>
        <cylinderGeometry args={[2.2, 2.2, 0.15, 64]} />
        <meshStandardMaterial
          color="#2d1b69"
          emissive="#4c1d95"
          emissiveIntensity={0.4}
          roughness={0.3}
          metalness={0.7}
        />
      </mesh>

      {/* Monitor Setup - More realistic */}
      <group ref={monitorRef} position={[0, -0.2, 0]}>
        {/* Monitor Base */}
        <mesh position={[0, -0.7, 0]}>
          <cylinderGeometry args={[0.25, 0.25, 0.08, 32]} />
          <meshStandardMaterial
            color="#1a1a2e"
            roughness={0.2}
            metalness={0.8}
          />
        </mesh>

        {/* Monitor Stand */}
        <mesh position={[0, -0.35, 0]}>
          <cylinderGeometry args={[0.04, 0.04, 0.7, 16]} />
          <meshStandardMaterial
            color="#1a1a2e"
            roughness={0.2}
            metalness={0.8}
          />
        </mesh>

        {/* Monitor Frame */}
        <mesh position={[0, 0.1, 0]} rotation={[0, 0, 0]}>
          <boxGeometry args={[1.8, 1.1, 0.08]} />
          <meshStandardMaterial
            color="#0f0f23"
            emissive="#1e1b4b"
            emissiveIntensity={0.2}
            roughness={0.1}
            metalness={0.9}
          />
        </mesh>

        {/* Monitor Screen - Dark background */}
        <mesh position={[0, 0.1, 0.05]}>
          <planeGeometry args={[1.6, 0.9]} />
          <meshStandardMaterial
            color="#000000"
            emissive="#0a0a0a"
            emissiveIntensity={0.1}
          />
        </mesh>

        {/* Code Lines - Colorful like target */}
        {/* Yellow lines */}
        <mesh position={[-0.3, 0.25, 0.06]}>
          <planeGeometry args={[0.6, 0.03]} />
          <meshStandardMaterial
            color="#fbbf24"
            emissive="#fbbf24"
            emissiveIntensity={0.8}
          />
        </mesh>
        <mesh position={[-0.1, 0.15, 0.06]}>
          <planeGeometry args={[0.8, 0.03]} />
          <meshStandardMaterial
            color="#fbbf24"
            emissive="#fbbf24"
            emissiveIntensity={0.8}
          />
        </mesh>

        {/* Pink/Magenta lines */}
        <mesh position={[-0.2, 0.05, 0.06]}>
          <planeGeometry args={[0.7, 0.03]} />
          <meshStandardMaterial
            color="#ec4899"
            emissive="#ec4899"
            emissiveIntensity={0.8}
          />
        </mesh>
        <mesh position={[-0.4, -0.05, 0.06]}>
          <planeGeometry args={[0.5, 0.03]} />
          <meshStandardMaterial
            color="#ec4899"
            emissive="#ec4899"
            emissiveIntensity={0.8}
          />
        </mesh>

        {/* Cyan lines */}
        <mesh position={[-0.1, -0.15, 0.06]}>
          <planeGeometry args={[0.9, 0.03]} />
          <meshStandardMaterial
            color="#06b6d4"
            emissive="#06b6d4"
            emissiveIntensity={0.8}
          />
        </mesh>
        <mesh position={[-0.3, -0.25, 0.06]}>
          <planeGeometry args={[0.6, 0.03]} />
          <meshStandardMaterial
            color="#06b6d4"
            emissive="#06b6d4"
            emissiveIntensity={0.8}
          />
        </mesh>
      </group>

      {/* Detailed Gears with Teeth */}
      <group ref={gear1Ref} position={[-1.8, 0.8, 0.3]}>
        <mesh>
          <primitive object={createGearGeometry(0.35, 12, 0.15)} />
          <meshStandardMaterial
            color="#7c3aed"
            emissive="#8b5cf6"
            emissiveIntensity={0.6}
            roughness={0.2}
            metalness={0.8}
          />
        </mesh>
      </group>

      <group ref={gear2Ref} position={[1.6, 0.5, -0.2]}>
        <mesh>
          <primitive object={createGearGeometry(0.28, 10, 0.12)} />
          <meshStandardMaterial
            color="#a855f7"
            emissive="#c084fc"
            emissiveIntensity={0.6}
            roughness={0.2}
            metalness={0.8}
          />
        </mesh>
      </group>

      <group ref={gear3Ref} position={[-1.2, -0.3, -0.6]}>
        <mesh>
          <primitive object={createGearGeometry(0.22, 8, 0.1)} />
          <meshStandardMaterial
            color="#6366f1"
            emissive="#818cf8"
            emissiveIntensity={0.6}
            roughness={0.2}
            metalness={0.8}
          />
        </mesh>
      </group>

      <group ref={gear4Ref} position={[1.2, 1.2, 0.8]}>
        <mesh>
          <primitive object={createGearGeometry(0.18, 6, 0.08)} />
          <meshStandardMaterial
            color="#8b5cf6"
            emissive="#a78bfa"
            emissiveIntensity={0.6}
            roughness={0.2}
            metalness={0.8}
          />
        </mesh>
      </group>

      {/* Enhanced Mobile Phone */}
      <group ref={phoneRef} position={[1.4, 0.3, 0.4]}>
        {/* Phone Body */}
        <mesh>
          <boxGeometry args={[0.35, 0.7, 0.08]} />
          <meshStandardMaterial
            color="#0f0f23"
            emissive="#1e1b4b"
            emissiveIntensity={0.3}
            roughness={0.1}
            metalness={0.9}
          />
        </mesh>

        {/* Phone Screen */}
        <mesh position={[0, 0, 0.05]}>
          <planeGeometry args={[0.3, 0.6]} />
          <meshStandardMaterial
            color="#000000"
            emissive="#00d4aa"
            emissiveIntensity={0.7}
          />
        </mesh>

        {/* Checkmark on screen */}
        <mesh position={[0, 0, 0.06]}>
          <planeGeometry args={[0.15, 0.15]} />
          <meshStandardMaterial
            color="#10b981"
            emissive="#10b981"
            emissiveIntensity={0.9}
          />
        </mesh>
      </group>

      {/* Enhanced Cloud */}
      <group ref={cloudRef} position={[-0.8, -0.6, 0.3]}>
        <mesh position={[0, 0, 0]}>
          <sphereGeometry args={[0.2, 32, 32]} />
          <meshStandardMaterial
            color="#0ea5e9"
            emissive="#38bdf8"
            emissiveIntensity={0.7}
            roughness={0.3}
            metalness={0.2}
          />
        </mesh>
        <mesh position={[-0.15, 0.08, 0]}>
          <sphereGeometry args={[0.15, 32, 32]} />
          <meshStandardMaterial
            color="#0ea5e9"
            emissive="#38bdf8"
            emissiveIntensity={0.7}
            roughness={0.3}
            metalness={0.2}
          />
        </mesh>
        <mesh position={[0.15, 0.08, 0]}>
          <sphereGeometry args={[0.12, 32, 32]} />
          <meshStandardMaterial
            color="#0ea5e9"
            emissive="#38bdf8"
            emissiveIntensity={0.7}
            roughness={0.3}
            metalness={0.2}
          />
        </mesh>
        <mesh position={[0, 0.15, 0]}>
          <sphereGeometry args={[0.1, 32, 32]} />
          <meshStandardMaterial
            color="#0ea5e9"
            emissive="#38bdf8"
            emissiveIntensity={0.7}
            roughness={0.3}
            metalness={0.2}
          />
        </mesh>
      </group>

      {/* Floating App Icons/Blocks */}
      <group position={[-1.6, 0.4, 0.2]}>
        <mesh>
          <boxGeometry args={[0.4, 0.4, 0.08]} />
          <meshStandardMaterial
            color="#0f0f23"
            emissive="#06b6d4"
            emissiveIntensity={0.6}
            roughness={0.1}
            metalness={0.8}
          />
        </mesh>
        {/* Code lines on the block */}
        <mesh position={[0, 0, 0.05]}>
          <planeGeometry args={[0.3, 0.05]} />
          <meshStandardMaterial
            color="#06b6d4"
            emissive="#06b6d4"
            emissiveIntensity={0.9}
          />
        </mesh>
        <mesh position={[0, -0.08, 0.05]}>
          <planeGeometry args={[0.25, 0.05]} />
          <meshStandardMaterial
            color="#06b6d4"
            emissive="#06b6d4"
            emissiveIntensity={0.9}
          />
        </mesh>
      </group>

      <group position={[0.9, 1.3, -0.3]}>
        <mesh>
          <boxGeometry args={[0.35, 0.35, 0.08]} />
          <meshStandardMaterial
            color="#0f0f23"
            emissive="#10b981"
            emissiveIntensity={0.6}
            roughness={0.1}
            metalness={0.8}
          />
        </mesh>
        {/* Network/connection icon */}
        <mesh position={[0, 0, 0.05]}>
          <sphereGeometry args={[0.03, 16, 16]} />
          <meshStandardMaterial
            color="#10b981"
            emissive="#10b981"
            emissiveIntensity={0.9}
          />
        </mesh>
        <mesh position={[0.08, 0.08, 0.05]}>
          <sphereGeometry args={[0.03, 16, 16]} />
          <meshStandardMaterial
            color="#10b981"
            emissive="#10b981"
            emissiveIntensity={0.9}
          />
        </mesh>
        <mesh position={[-0.08, -0.08, 0.05]}>
          <sphereGeometry args={[0.03, 16, 16]} />
          <meshStandardMaterial
            color="#10b981"
            emissive="#10b981"
            emissiveIntensity={0.9}
          />
        </mesh>
      </group>
    </group>
  );
};

// Loading fallback component
const ModelFallback = () => (
  <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl flex items-center justify-center">
    <div className="text-center">
      <div className="w-16 h-16 border-4 border-electric-indigo border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
      <p className="text-gray-400">Loading 3D Model...</p>
    </div>
  </div>
);

// Main 3D Scene Component
const ThreeModelScene: React.FC<{ className?: string }> = ({ className = "" }) => {
  return (
    <motion.div
      className={`relative ${className}`}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 1, delay: 0.5 }}
    >
      <div className="w-full h-full rounded-2xl overflow-hidden bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50">
        <Canvas
          camera={{ position: [0, 0, 5], fov: 50 }}
          style={{ width: '100%', height: '100%' }}
          gl={{ antialias: true, alpha: true }}
        >
          {/* Lighting */}
          <ambientLight intensity={0.4} />
          <directionalLight position={[10, 10, 5]} intensity={1} />
          <pointLight position={[-10, -10, -10]} intensity={0.5} />
          
          {/* Environment for reflections */}
          <Environment preset="city" />
          
          {/* 3D Model */}
          <Suspense fallback={<ModelFallback />}>
            <CoolManModel />
          </Suspense>
          
          {/* Contact shadows for realism */}
          <ContactShadows
            position={[0, -2, 0]}
            opacity={0.4}
            scale={10}
            blur={2}
            far={4}
          />
          
          {/* Controls for interaction */}
          <OrbitControls
            enablePan={false}
            enableZoom={false}
            enableRotate={true}
            autoRotate={true}
            autoRotateSpeed={2}
            maxPolarAngle={Math.PI / 2}
            minPolarAngle={Math.PI / 3}
          />
        </Canvas>
        
        {/* Overlay gradient for better integration */}
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/20 to-transparent pointer-events-none" />
      </div>
    </motion.div>
  );
};

// Software Scene 3D Scene Component
const SoftwareSceneThree: React.FC<{ className?: string }> = ({ className = "" }) => {
  return (
    <motion.div
      className={`relative ${className}`}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 1, delay: 0.5 }}
    >
      <div className="w-full h-full rounded-2xl overflow-hidden bg-gradient-to-br from-purple-900/10 to-blue-900/5 backdrop-blur-sm border border-purple-500/20">
        <Canvas
          camera={{ position: [0, 0, 4], fov: 50 }}
          style={{ width: '100%', height: '100%' }}
          gl={{ antialias: true, alpha: true }}
        >
          {/* Enhanced Lighting for Maximum Vibrancy */}
          <ambientLight intensity={0.3} />
          <directionalLight position={[10, 10, 5]} intensity={0.6} color="#ffffff" />

          {/* Multiple colored point lights for glow effects */}
          <pointLight position={[-3, 3, 3]} intensity={2.0} color="#8b5cf6" distance={8} decay={2} />
          <pointLight position={[3, -2, 3]} intensity={1.8} color="#06b6d4" distance={8} decay={2} />
          <pointLight position={[0, 2, -3]} intensity={1.5} color="#a855f7" distance={8} decay={2} />
          <pointLight position={[-2, -2, 2]} intensity={1.2} color="#ec4899" distance={6} decay={2} />
          <pointLight position={[2, 3, -2]} intensity={1.0} color="#10b981" distance={6} decay={2} />

          {/* Spotlight for dramatic effect */}
          <spotLight
            position={[0, 8, 0]}
            angle={0.6}
            penumbra={0.5}
            intensity={1.5}
            color="#8b5cf6"
            target-position={[0, 0, 0]}
          />

          {/* Environment for reflections */}
          <Environment preset="night" />

          {/* 3D Model */}
          <Suspense fallback={<ModelFallback />}>
            <CustomSoftwareScene />
          </Suspense>

          {/* Contact shadows for realism */}
          <ContactShadows
            position={[0, -2, 0]}
            opacity={0.3}
            scale={8}
            blur={1.5}
            far={3}
          />

          {/* Controls for interaction */}
          <OrbitControls
            enablePan={false}
            enableZoom={false}
            enableRotate={true}
            autoRotate={true}
            autoRotateSpeed={1}
            maxPolarAngle={Math.PI / 2}
            minPolarAngle={Math.PI / 3}
          />

          {/* Post-processing effects for enhanced glow */}
          <EffectComposer>
            <Bloom
              intensity={1.5}
              luminanceThreshold={0.2}
              luminanceSmoothing={0.9}
              height={300}
            />
            <ToneMapping adaptive={true} />
          </EffectComposer>
        </Canvas>

        {/* Overlay gradient for better integration */}
        <div className="absolute inset-0 bg-gradient-to-t from-purple-900/5 to-transparent pointer-events-none" />
      </div>
    </motion.div>
  );
};

// Preload the models
useGLTF.preload('/models/cool man.glb');
useGLTF.preload('/models/software_scene.glb');

export default ThreeModelScene;
export { SoftwareSceneThree };
