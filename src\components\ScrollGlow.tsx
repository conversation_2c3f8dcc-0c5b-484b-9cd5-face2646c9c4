'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

const ScrollGlow = () => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Define multiple circles that move around text content areas
  const circles = [
    {
      id: 1,
      baseX: 20, // Left side
      baseY: 30,
      size: 300,
      color: 'bg-royal-purple/6',
      speed: 0.0008,
      amplitude: 100,
    },
    {
      id: 2,
      baseX: 80, // Right side
      baseY: 60,
      size: 250,
      color: 'bg-electric-indigo/5',
      speed: 0.0006,
      amplitude: 80,
    },
    {
      id: 3,
      baseX: 50, // Center
      baseY: 80,
      size: 200,
      color: 'bg-royal-purple/4',
      speed: 0.001,
      amplitude: 60,
    },
    {
      id: 4,
      baseX: 15, // Left-center
      baseY: 70,
      size: 180,
      color: 'bg-sky-blue/3',
      speed: 0.0007,
      amplitude: 70,
    },
    {
      id: 5,
      baseX: 85, // Right-center
      baseY: 20,
      size: 220,
      color: 'bg-electric-indigo/4',
      speed: 0.0009,
      amplitude: 90,
    }
  ];

  return (
    <>
      {circles.map((circle) => {
        // Calculate movement for each circle based on scroll
        const scrollFactor = scrollY * circle.speed;

        // Create circular/orbital movement around the base position
        const xMovement = Math.sin(scrollFactor) * circle.amplitude;
        const yMovement = Math.cos(scrollFactor * 0.8) * (circle.amplitude * 0.6);

        // Add some drift based on scroll position
        const drift = {
          x: Math.sin(scrollY * 0.0003 + circle.id) * 40,
          y: Math.cos(scrollY * 0.0002 + circle.id) * 30,
        };

        // Calculate final position
        const finalX = circle.baseX + (xMovement / 10) + (drift.x / 10);
        const finalY = circle.baseY + (yMovement / 10) + (drift.y / 10);

        // Dynamic opacity and scale
        const opacity = 0.4 + Math.abs(Math.sin(scrollY * 0.0004 + circle.id)) * 0.3;
        const scale = 0.8 + Math.sin(scrollY * 0.0005 + circle.id) * 0.2;

        return (
          <motion.div
            key={circle.id}
            className={`fixed ${circle.color} rounded-full blur-3xl pointer-events-none`}
            style={{
              width: `${circle.size}px`,
              height: `${circle.size}px`,
              left: `${finalX}%`,
              top: `${finalY}%`,
              transform: `translate(-50%, -50%) scale(${scale})`,
              opacity: opacity,
              zIndex: -1,
            }}
            transition={{
              type: "spring",
              stiffness: 20,
              damping: 30,
              mass: 1,
            }}
          />
        );
      })}
    </>
  );
};

export default ScrollGlow;
