import { useState, useEffect } from 'react';

export type ScrollDirection = 'up' | 'down' | 'idle';

interface UseDirectionalScrollOptions {
  threshold?: number;
  debounceMs?: number;
}

export const useDirectionalScroll = (options: UseDirectionalScrollOptions = {}) => {
  const { threshold = 10, debounceMs = 50 } = options;
  const [scrollDirection, setScrollDirection] = useState<ScrollDirection>('idle');
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const updateScrollDirection = () => {
      const scrollY = window.scrollY;
      const direction = scrollY > lastScrollY + threshold ? 'down' : 
                      scrollY < lastScrollY - threshold ? 'up' : 'idle';

      if (direction !== scrollDirection && direction !== 'idle') {
        setScrollDirection(direction);
      }

      setLastScrollY(scrollY);
    };

    const onScroll = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateScrollDirection, debounceMs);
    };

    window.addEventListener('scroll', onScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', onScroll);
      clearTimeout(timeoutId);
    };
  }, [scrollDirection, lastScrollY, threshold, debounceMs]);

  return scrollDirection;
};

export default useDirectionalScroll;
