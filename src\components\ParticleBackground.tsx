'use client';

import React, { useEffect, useRef } from 'react';

interface Ball {
  x: number;
  y: number;
  targetX: number;
  targetY: number;
  size: number;
  opacity: number;
  targetOpacity: number;
  color: string;
  glowSize: number;
  isVisible: boolean;
}

const ParticleBackground: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const ballRef = useRef<Ball | null>(null);
  const scrollYRef = useRef<number>(0);
  const lastScrollRef = useRef<number>(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Ball color - subtle purple
    const ballColor = '#8b5cf6'; // purple variant

    // Scroll event handler
    const handleScroll = () => {
      scrollYRef.current = window.scrollY;
    };

    // Initialize single large ball
    const initBall = () => {
      ballRef.current = {
        x: -200, // Start off-screen left
        y: canvas.height / 2,
        targetX: canvas.width / 3, // Target position behind text
        targetY: canvas.height / 2,
        size: 150, // Large ball
        opacity: 0,
        targetOpacity: 0.08, // Very light opacity
        color: ballColor,
        glowSize: 300, // Large glow radius
        isVisible: false,
      };
    };

    // Get scroll-based position
    const getScrollBasedPosition = () => {
      const scrollProgress = scrollYRef.current / (document.documentElement.scrollHeight - window.innerHeight);
      const clampedProgress = Math.max(0, Math.min(1, scrollProgress));

      // Ball moves across screen as user scrolls
      const targetX = -200 + (canvas.width + 400) * clampedProgress;
      const targetY = canvas.height / 2 + Math.sin(clampedProgress * Math.PI * 2) * 100; // Slight vertical wave

      return { targetX, targetY, scrollProgress: clampedProgress };
    };

    // Animation function
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      if (!ballRef.current) return;

      const ball = ballRef.current;
      const { targetX, targetY, scrollProgress } = getScrollBasedPosition();

      // Update ball target position based on scroll
      ball.targetX = targetX;
      ball.targetY = targetY;

      // Determine if ball should be visible (when in viewport area)
      const shouldBeVisible = scrollProgress > 0.05 && scrollProgress < 0.95;
      ball.targetOpacity = shouldBeVisible ? 0.08 : 0;

      // Smooth interpolation to target position and opacity
      const lerpFactor = 0.05; // Smooth movement
      ball.x += (ball.targetX - ball.x) * lerpFactor;
      ball.y += (ball.targetY - ball.y) * lerpFactor;
      ball.opacity += (ball.targetOpacity - ball.opacity) * lerpFactor;

      // Only draw if ball has some opacity
      if (ball.opacity > 0.001) {
        // Create very light glow effect
        const gradient = ctx.createRadialGradient(
          ball.x, ball.y, 0,
          ball.x, ball.y, ball.glowSize
        );

        // Extremely subtle glow
        gradient.addColorStop(0, `${ball.color}${Math.floor(ball.opacity * 0.3 * 255).toString(16).padStart(2, '0')}`);
        gradient.addColorStop(0.2, `${ball.color}${Math.floor(ball.opacity * 0.15 * 255).toString(16).padStart(2, '0')}`);
        gradient.addColorStop(0.5, `${ball.color}${Math.floor(ball.opacity * 0.05 * 255).toString(16).padStart(2, '0')}`);
        gradient.addColorStop(0.8, `${ball.color}${Math.floor(ball.opacity * 0.02 * 255).toString(16).padStart(2, '0')}`);
        gradient.addColorStop(1, 'transparent');

        // Draw glow
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(ball.x, ball.y, ball.glowSize, 0, Math.PI * 2);
        ctx.fill();

        // Draw ball core (very subtle)
        ctx.fillStyle = `${ball.color}${Math.floor(ball.opacity * 0.4 * 255).toString(16).padStart(2, '0')}`;
        ctx.beginPath();
        ctx.arc(ball.x, ball.y, ball.size, 0, Math.PI * 2);
        ctx.fill();
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    initBall();
    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      window.removeEventListener('scroll', handleScroll);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="fixed top-0 left-0 w-full h-full pointer-events-none"
      style={{ zIndex: -1 }}
    />
  );
};

export default ParticleBackground;
