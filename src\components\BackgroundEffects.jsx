import React, { useEffect, useRef, useState } from 'react';
import Particles from 'react-tsparticles';
import { loadFull } from 'tsparticles';

// Unsplash Nebula and Grid Overlay URLs
const NEBULA_URL = 'https://images.unsplash.com/photo-1462331940025-496dfbfc7564?auto=format&fit=crop&w=1200&q=80';
const GRID_URL = 'https://www.transparenttextures.com/patterns/hexellence.png'; // subtle hex grid

// Helper for random positions
const random = (min, max) => Math.random() * (max - min) + min;

export default function BackgroundEffects() {
  // Shooting star state
  const [shooting, setShooting] = useState(false);
  const shootingTimeout = useRef();

  // Parallax state
  const [parallax, setParallax] = useState({ x: 0, y: 0 });

  // Sparkle state
  const [sparkles, setSparkles] = useState([
    { id: 1, left: '20%', top: '60%', delay: 0 },
    { id: 2, left: '70%', top: '30%', delay: 2 },
    { id: 3, left: '50%', top: '80%', delay: 4 },
  ]);

  // Parallax effect (mouse move)
  useEffect(() => {
    const handleMove = (e) => {
      const x = (e.clientX / window.innerWidth - 0.5) * 2;
      const y = (e.clientY / window.innerHeight - 0.5) * 2;
      setParallax({ x, y });
    };
    window.addEventListener('mousemove', handleMove);
    return () => window.removeEventListener('mousemove', handleMove);
  }, []);

  // Shooting star interval
  useEffect(() => {
    const trigger = () => {
      setShooting(true);
      shootingTimeout.current = setTimeout(() => setShooting(false), 1200);
    };
    const interval = setInterval(trigger, 20000);
    return () => {
      clearInterval(interval);
      clearTimeout(shootingTimeout.current);
    };
  }, []);

  // Starfield config
  const particlesInit = async (main) => {
    await loadFull(main);
  };
  const particlesOptions = {
    fullScreen: false,
    background: { color: 'transparent' },
    fpsLimit: 60,
    particles: {
      number: { value: 60, density: { enable: true, area: 1200 } },
      color: { value: '#fff' },
      shape: { type: 'circle' },
      opacity: { value: 0.5, random: true },
      size: { value: 1.5, random: { enable: true, minimumValue: 0.5 } },
      move: {
        enable: true,
        speed: 0.15,
        direction: 'none',
        random: true,
        straight: false,
        outModes: { default: 'out' },
        attract: { enable: false },
      },
      links: {
        enable: true,
        distance: 120,
        color: '#a855f7',
        opacity: 0.15,
        width: 1,
      },
      glow: {
        enable: false,
      },
    },
    interactivity: {
      events: {
        onHover: { enable: true, mode: 'repulse' },
        resize: true,
      },
      modes: {
        repulse: { distance: 80, duration: 0.4 },
        connect: { distance: 120, radius: 60 },
      },
    },
    detectRetina: true,
    zIndex: 1,
  };

  // Blobs config
  const blobs = [
    {
      style: {
        background: 'radial-gradient(circle at 30% 30%, #6c63ff 0%, transparent 70%)',
        filter: 'blur(100px)',
        opacity: 0.18,
        width: 400,
        height: 400,
        top: '-120px',
        left: '-120px',
        animation: 'blobMove1 18s ease-in-out infinite',
      },
    },
    {
      style: {
        background: 'radial-gradient(circle at 70% 70%, #8e44ad 0%, transparent 70%)',
        filter: 'blur(120px)',
        opacity: 0.13,
        width: 350,
        height: 350,
        bottom: '-100px',
        right: '-100px',
        animation: 'blobMove2 22s ease-in-out infinite',
      },
    },
    {
      style: {
        background: 'radial-gradient(circle at 60% 40%, #1abc9c 0%, transparent 70%)',
        filter: 'blur(90px)',
        opacity: 0.12,
        width: 300,
        height: 300,
        top: '60%',
        left: '60%',
        animation: 'blobMove3 20s ease-in-out infinite',
      },
    },
  ];

  // Geometric shapes config
  const shapes = [
    {
      svg: (
        <svg width="80" height="80" viewBox="0 0 80 80" fill="none"><circle cx="40" cy="40" r="36" stroke="#fff" strokeOpacity="0.08" strokeWidth="4"/></svg>
      ),
      style: {
        position: 'absolute',
        top: '10%',
        left: '5%',
        opacity: 0.12,
        animation: 'geoRotate1 30s linear infinite',
      },
    },
    {
      svg: (
        <svg width="60" height="60" viewBox="0 0 60 60" fill="none"><polygon points="30,5 55,55 5,55" stroke="#fff" strokeOpacity="0.09" strokeWidth="3" fill="none"/></svg>
      ),
      style: {
        position: 'absolute',
        bottom: '12%',
        right: '8%',
        opacity: 0.10,
        animation: 'geoRotate2 40s linear infinite',
      },
    },
    {
      svg: (
        <svg width="70" height="70" viewBox="0 0 70 70" fill="none"><polygon points="35,10 60,60 10,60" stroke="#fff" strokeOpacity="0.07" strokeWidth="2" fill="none"/></svg>
      ),
      style: {
        position: 'absolute',
        top: '70%',
        left: '80%',
        opacity: 0.09,
        animation: 'geoRotate3 36s linear infinite',
      },
    },
  ];

  // Parallax transform helper
  const parallaxStyle = (depth = 1) => ({
    transform: `translate3d(${parallax.x * depth * 10}px, ${parallax.y * depth * 10}px, 0)`
  });

  return (
    <>
      {/* Starfield (Background) */}
      <div className="pointer-events-none fixed inset-0 w-full h-full z-[-10]" style={parallaxStyle(0.2)}>
        <Particles id="tsparticles" init={particlesInit} options={particlesOptions} style={{ width: '100%', height: '100%' }} />
      </div>

      {/* Glowing Animated Blobs */}
      {blobs.map((blob, i) => (
        <div
          key={i}
          className="pointer-events-none fixed z-[-9] rounded-full"
          style={{ ...blob.style, position: 'fixed' }}
        />
      ))}

      {/* Nebula/Galaxy Texture */}
      <img
        src={NEBULA_URL}
        alt="Nebula texture"
        className="pointer-events-none fixed z-[-8] w-full h-full object-cover"
        style={{
          opacity: 0.07,
          filter: 'blur(8px)',
          top: 0,
          left: 0,
        }}
        draggable={false}
      />

      {/* Sparkle Drift Animation */}
      {sparkles.map((s, i) => (
        <div
          key={s.id}
          className="pointer-events-none fixed z-[-7]"
          style={{
            left: s.left,
            top: s.top,
            width: 12,
            height: 12,
            opacity: 0.7,
            filter: 'drop-shadow(0 0 8px #fff)',
            animation: `sparkleDrift${i + 1} 8s ${s.delay}s linear infinite`,
          }}
        >
          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
            <circle cx="6" cy="6" r="3" fill="#fff" fillOpacity="0.8" />
            <circle cx="6" cy="6" r="2" fill="#38bdf8" fillOpacity="0.5" />
          </svg>
        </div>
      ))}

      {/* Parallax Foreground Layer (empty, but for future use) */}
      {/* <div className="pointer-events-none fixed inset-0 z-[-6]" style={parallaxStyle(0.5)} /> */}

      {/* Shooting Star */}
      {shooting && (
        <div
          className="pointer-events-none fixed z-[-5]"
          style={{
            top: '20%',
            left: '-10%',
            width: '180px',
            height: '2px',
            background: 'linear-gradient(90deg, #fff 0%, #fff0 100%)',
            boxShadow: '0 0 16px 4px #fff',
            opacity: 0.8,
            borderRadius: 2,
            filter: 'blur(0.5px)',
            animation: 'shootingStar 1.2s linear',
          }}
        />
      )}

      {/* Floating Geometric Shapes */}
      {shapes.map((shape, i) => (
        <div
          key={i}
          className="pointer-events-none z-[-4]"
          style={shape.style}
        >
          {shape.svg}
        </div>
      ))}

      {/* Futuristic Tech Grid Overlay */}
      <div
        className="pointer-events-none fixed inset-0 w-full h-full z-[-11]"
        style={{
          backgroundImage: `url(${GRID_URL})`,
          opacity: 0.06,
          backgroundRepeat: 'repeat',
          backgroundSize: '120px 120px',
          animation: 'gridScroll 60s linear infinite',
        }}
      />

      {/* Keyframes (scoped in a style tag) */}
      <style>{`
        @keyframes blobMove1 {
          0%, 100% { transform: scale(1) translate(0, 0); }
          50% { transform: scale(1.08) translate(40px, 30px); }
        }
        @keyframes blobMove2 {
          0%, 100% { transform: scale(1) translate(0, 0); }
          50% { transform: scale(1.12) translate(-30px, 40px); }
        }
        @keyframes blobMove3 {
          0%, 100% { transform: scale(1) translate(0, 0); }
          50% { transform: scale(1.05) translate(20px, -20px); }
        }
        @keyframes sparkleDrift1 {
          0% { opacity: 0; transform: translateY(0) scale(1); }
          10% { opacity: 1; }
          80% { opacity: 1; transform: translateY(-40px) scale(1.2); }
          100% { opacity: 0; transform: translateY(-60px) scale(0.8); }
        }
        @keyframes sparkleDrift2 {
          0% { opacity: 0; transform: translateX(0) scale(1); }
          10% { opacity: 1; }
          80% { opacity: 1; transform: translateX(40px) scale(1.2); }
          100% { opacity: 0; transform: translateX(60px) scale(0.8); }
        }
        @keyframes sparkleDrift3 {
          0% { opacity: 0; transform: translateY(0) scale(1); }
          10% { opacity: 1; }
          80% { opacity: 1; transform: translateY(30px) scale(1.2); }
          100% { opacity: 0; transform: translateY(60px) scale(0.8); }
        }
        @keyframes shootingStar {
          0% { left: -10%; opacity: 0; }
          10% { opacity: 1; }
          80% { left: 80%; opacity: 1; }
          100% { left: 110%; opacity: 0; }
        }
        @keyframes geoRotate1 {
          0% { transform: rotate(0deg) translateY(0); }
          100% { transform: rotate(360deg) translateY(10px); }
        }
        @keyframes geoRotate2 {
          0% { transform: rotate(0deg) translateY(0); }
          100% { transform: rotate(360deg) translateY(-10px); }
        }
        @keyframes geoRotate3 {
          0% { transform: rotate(0deg) translateY(0); }
          100% { transform: rotate(360deg) translateY(8px); }
        }
        @keyframes gridScroll {
          0% { background-position: 0 0; }
          100% { background-position: 120px 240px; }
        }
      `}</style>
    </>
  );
} 