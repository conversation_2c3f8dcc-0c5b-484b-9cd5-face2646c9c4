'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { CodeIcon, UserIcon, FaGithub } from './icons';

// Typewriter Effect Component
const TypewriterEffect: React.FC<{
  text: string;
  delay?: number;
  speed?: number;
  className?: string;
}> = ({ text, delay = 0, speed = 100, className = "" }) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTypingComplete, setIsTypingComplete] = useState(false);

  useEffect(() => {
    if (currentIndex < text.length) {
      const typingTimeout = setTimeout(() => {
        setDisplayText(text.slice(0, currentIndex + 1));
        setCurrentIndex(currentIndex + 1);
      }, currentIndex === 0 ? delay : speed);

      return () => clearTimeout(typingTimeout);
    } else if (!isTypingComplete) {
      setIsTypingComplete(true);
    }
  }, [currentIndex, text, delay, speed, isTypingComplete]);

  return (
    <span className={className}>
      {displayText}
      {!isTypingComplete && (
        <span className="inline-block w-0.5 h-5 bg-electric-indigo ml-1">
          <span className="cursor" />
        </span>
      )}
    </span>
  );
};

interface LandingPageProps {
  onEnter: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onEnter }) => {
  // Auto-redirect after 3 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      onEnter();
    }, 3000);

    // Cleanup timer if component unmounts or user clicks before timer
    return () => clearTimeout(timer);
  }, [onEnter]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      y: -100,
      transition: {
        duration: 1,
        ease: "easeInOut"
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  const iconVariants = {
    hidden: { scale: 0, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "backOut",
        delay: 0.5
      }
    },
    hover: {
      scale: 1.1,
      transition: {
        duration: 0.2,
        ease: "easeInOut"
      }
    }
  };

  const textGradientVariants = {
    hidden: { backgroundPosition: "0% 50%" },
    visible: {
      backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
      transition: {
        duration: 3,
        ease: "easeInOut",
        repeat: Infinity,
        repeatType: "loop" as const
      }
    }
  };

  return (
    <motion.section
      className="min-h-screen flex flex-col justify-center items-center text-center relative overflow-hidden cursor-pointer"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      onClick={onEnter}
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-rich-black via-deep-violet/20 to-rich-black" />
      
      {/* Animated background particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[
          { left: '10%', top: '20%' },
          { left: '20%', top: '50%' },
          { left: '30%', top: '80%' },
          { left: '40%', top: '30%' },
          { left: '50%', top: '60%' },
          { left: '60%', top: '10%' },
          { left: '70%', top: '40%' },
          { left: '80%', top: '70%' },
          { left: '90%', top: '25%' },
          { left: '15%', top: '75%' },
          { left: '25%', top: '35%' },
          { left: '35%', top: '65%' },
          { left: '45%', top: '15%' },
          { left: '55%', top: '45%' },
          { left: '65%', top: '85%' },
          { left: '75%', top: '55%' },
          { left: '85%', top: '5%' },
          { left: '95%', top: '95%' },
          { left: '5%', top: '85%' },
          { left: '15%', top: '15%' }
        ].map((position, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-electric-indigo/30 rounded-full"
            style={{
              left: position.left,
              top: position.top,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 3 + (i % 3),
              repeat: Infinity,
              delay: i * 0.2,
            }}
          />
        ))}
      </div>

      <motion.div
        className="relative z-10 px-6 max-w-7xl mx-auto"
        variants={containerVariants}
      >
        {/* Icon buttons */}
        <motion.div 
          className="flex space-x-12 mb-12 justify-center"
          variants={itemVariants}
        >
          {/* Inspired by eki.my.id: Code, User, GitHub icons with purple glow */}
          {[
            { icon: CodeIcon, label: "Projects" },
            { icon: UserIcon, label: "About Me" },
            { icon: FaGithub, label: "GitHub" }
          ].map(({ icon: Icon, label }, index) => (
            <motion.button
              key={label}
              className="group relative p-5 rounded-full bg-[#1a1333] shadow-lg flex items-center justify-center"
              style={{ boxShadow: '0 0 24px 6px #655dbb55' }}
              variants={iconVariants}
              whileHover="hover"
              whileTap={{ scale: 0.95 }}
            >
              <Icon className="w-8 h-8 text-white" />
            </motion.button>
          ))}
        </motion.div>

        {/* Main heading */}
        <motion.h1 
          className="text-9xl md:text-6xl lg:text-9xl font-bold mb-12 tracking-wide"
          variants={itemVariants}
        >
          <span className="text-white-smoke">Welcome To My </span>
          <motion.span
            className="text-transparent bg-clip-text bg-gradient-to-r from-royal-purple via-electric-indigo to-royal-purple bg-[length:200%_100%]"
            variants={textGradientVariants}
            style={{
              backgroundSize: "200% 100%",
            }}
          >
            Portfolio Website
          </motion.span>
        </motion.h1>

        {/* Subheading */}
        <motion.p 
          className="text-3xl md:text-[1.85rem] text-gray-400 mt-10 w-max-full mb-16"
          variants={itemVariants}
        >
          Data Science & AI Enthusiast | Building Intelligent Solutions | Computer Science Undergraduate
        </motion.p>

        {/* Click/Scroll hint */}
        <motion.div 
          className="mt-16"
          variants={itemVariants}
        >
          <motion.div
            className="text-2xl md:text-base text-electric-indigo font-mono tracking-wider mb-4"
            whileHover={{
              scale: 1.05,
              textShadow: "0 0 10px rgba(139, 92, 246, 0.5)"
            }}
            transition={{ duration: 0.2 }}
          >
            <span className="text-gray-400 mr-2">📧</span>
            <TypewriterEffect
              text="<EMAIL>"
              delay={1500}
              speed={80}
              className="text-electric-indigo text-xl"
            />
          </motion.div>
          
          <motion.div
            className="text-gray-400 text-2xl flex items-center justify-center space-x-2 mt-8"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <span>Click anywhere to enter</span>
            <motion.span
              animate={{ x: [0, 5, 0] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              →
            </motion.span>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Scroll indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
        variants={itemVariants}
      >
        <div className="w-6 h-10 border-2 border-electric-indigo rounded-full flex justify-center">
          <motion.div 
            className="w-1 h-3 bg-electric-indigo rounded-full mt-2"
            animate={{ y: [0, 16, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </div>
      </motion.div>

      {/* Subtle glow effect */}
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-royal-purple/10 rounded-full blur-3xl" />
    </motion.section>
  );
};

export default LandingPage;
