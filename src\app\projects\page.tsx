"use client";
import React from 'react';
import { FaGithub, FaLinkedin, FaEnvelope, FaPhoneAlt, FaMapMarkerAlt } from 'react-icons/fa';
import GlowingOrbsBackground from '@/components/GlowingOrbsBackground';

const ProjectsPage = () => {
  return (
    <>
      <GlowingOrbsBackground />
      <div className="min-h-screen bg-gradient-to-br from-rich-black via-black/90 to-deep-violet/90 text-white-smoke font-sans">
        {/* Header */}
        <header className="pt-24 pb-12 text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-4 backend-glow">Projects</h1>
          <p className="text-xl md:text-2xl text-electric-indigo max-w-2xl mx-auto">A modern web application showcase with a dark, neon-inspired theme.</p>
          {/* Social Icons */}
          <div className="flex space-x-4">
            <a href="https://github.com/Jugalsoni18" target="_blank" rel="noopener noreferrer" className="text-white/60 hover:text-white transition">
              <FaGithub className="text-3xl" />
            </a>
            <a href="https://www.linkedin.com/in/jugal-soni-8bb797308?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app" target="_blank" rel="noopener noreferrer" className="text-white/60 hover:text-white transition">
              <FaLinkedin className="text-3xl" />
            </a>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-[95vw] mx-auto flex flex-col md:flex-row md:justify-between gap-12 md:gap-32 lg:gap-44 xl:gap-56 text-lg md:text-xl lg:text-2xl">
          {/* Project Details */}
          <section className="lg:col-span-2">
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-white mb-6">Project Overview</h2>
              <div className="bg-black/60 rounded-2xl neon-glow border border-royal-purple/30 shadow-xl overflow-hidden mb-8">
                <img src="https://placehold.co/800x500/4f46e5/white?text=Project+Screenshot" alt="Project Screenshot" className="w-full h-auto object-cover" />
              </div>
              <p className="text-lg text-white/80 mb-6 leading-relaxed">
                This project is a comprehensive web application designed to solve real-world problems with an elegant and intuitive interface. It incorporates modern web technologies to deliver a seamless user experience across all devices.
              </p>
              <p className="text-lg text-white/80 mb-6 leading-relaxed">
                The application features a responsive design, secure authentication, real-time data updates, and interactive elements that engage users while maintaining high performance standards.
              </p>
            </div>

            {/* Features */}
            <div className="mb-12">
              <h2 className="text-3xl font-bold text-white mb-6">Key Features</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[
                  { icon: <i className="fas fa-bolt" />, title: 'Fast Performance', desc: 'Optimized for speed with lazy loading and efficient data fetching.' },
                  { icon: <i className="fas fa-mobile-alt" />, title: 'Responsive Design', desc: 'Works perfectly on all devices from mobile to desktop.' },
                  { icon: <i className="fas fa-shield-alt" />, title: 'Secure Authentication', desc: 'Implements industry-standard security protocols.' },
                  { icon: <i className="fas fa-sync-alt" />, title: 'Real-time Updates', desc: 'Live data synchronization across all connected clients.' },
                ].map((f, i) => (
                  <div key={i} className="bg-black/60 p-6 rounded-2xl neon-glow border border-royal-purple/20 shadow-md hover:shadow-purple-500/20 transition">
                    <div className="text-electric-indigo mb-3 text-2xl">{f.icon}</div>
                    <h3 className="text-xl font-semibold mb-2 text-white-smoke">{f.title}</h3>
                    <p className="text-white/70">{f.desc}</p>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Sidebar */}
          <aside className="bg-black/60 rounded-2xl neon-glow border border-royal-purple/30 shadow-lg p-6 sticky top-6 h-fit">
            <h2 className="text-2xl font-bold text-white mb-6">Project Details</h2>
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3">Technologies Used</h3>
              <div className="flex flex-wrap gap-3">
                {[
                  { icon: <i className="fab fa-react mr-2" />, name: 'React' },
                  { icon: <i className="fab fa-node-js mr-2" />, name: 'Node.js' },
                  { icon: <i className="fas fa-database mr-2" />, name: 'MongoDB' },
                  { icon: <i className="fab fa-js mr-2" />, name: 'JavaScript' },
                  { icon: <i className="fab fa-css3-alt mr-2" />, name: 'Tailwind CSS' },
                ].map((tech, i) => (
                  <span key={i} className="tech-icon bg-royal-purple/10 text-electric-indigo px-3 py-1 rounded-full text-sm flex items-center border border-royal-purple/30">
                    {tech.icon} {tech.name}
                  </span>
                ))}
              </div>
            </div>
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3">Project Info</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-white/70">Status</span>
                  <span className="font-medium text-white">Completed</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">Duration</span>
                  <span className="font-medium text-white">3 Months</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">Team Size</span>
                  <span className="font-medium text-white">3 People</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">My Role</span>
                  <span className="font-medium text-white">Full-stack Developer</span>
                </div>
              </div>
            </div>
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3">Live Preview</h3>
              <a href="#" className="block w-full bg-electric-indigo hover:bg-royal-purple text-white text-center py-2 px-4 rounded-lg transition duration-300 font-semibold shadow-lg">
                Visit Website <i className="fas fa-external-link-alt ml-2" />
              </a>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-3">Source Code</h3>
              <div className="space-y-3">
                <a href="#" className="flex items-center justify-between bg-black/40 hover:bg-royal-purple/20 p-3 rounded-lg transition border border-royal-purple/20">
                  <span>Frontend Repository</span>
                  <i className="fas fa-arrow-right text-electric-indigo" />
                </a>
                <a href="#" className="flex items-center justify-between bg-black/40 hover:bg-royal-purple/20 p-3 rounded-lg transition border border-royal-purple/20">
                  <span>Backend Repository</span>
                  <i className="fas fa-arrow-right text-electric-indigo" />
                </a>
              </div>
            </div>
            <div>
              <h3 className="text-white text-lg font-semibold mb-4">Contact</h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <FaEnvelope className="mr-2 mt-1" />
                  <span><EMAIL></span>
                </li>
                <li className="flex items-start">
                  <FaPhoneAlt className="mr-2 mt-1" />
                  <span>9054714583</span>
                </li>
                <li className="flex items-start">
                  <FaMapMarkerAlt className="mr-2 mt-1" />
                  <span>Vadodara, Gujarat</span>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-white text-lg font-semibold mb-4">Services</h3>
              <ul className="space-y-2">
                <li><a href="#" className="hover:text-white transition">AI-Powered Web Development</a></li>
                <li><a href="#" className="hover:text-white transition">Full-Stack Development</a></li>
                <li><a href="#" className="hover:text-white transition">AI & Machine Learning Solutions</a></li>
                <li><a href="#" className="hover:text-white transition">Chatbot & Voice Assistant Development</a></li>
                <li><a href="#" className="hover:text-white transition">Data Science & Automation</a></li>
              </ul>
            </div>
          </aside>
        </main>
      </div>
    </>
  );
};

export default ProjectsPage; 