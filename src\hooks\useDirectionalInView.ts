import { useState, useEffect, useRef } from 'react';
import { useDirectionalScroll } from './useDirectionalScroll';

interface UseDirectionalInViewOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
  requireDownwardScroll?: boolean;
}

export const useDirectionalInView = (options: UseDirectionalInViewOptions = {}) => {
  const {
    threshold = 0.1,
    rootMargin = '0px',
    triggerOnce = true,
    requireDownwardScroll = true
  } = options;

  const [isInView, setIsInView] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);
  const scrollDirection = useDirectionalScroll();

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        const isVisible = entry.isIntersecting;
        
        if (isVisible && !hasTriggered) {
          // Only trigger if we don't require downward scroll, or if we're scrolling down
          if (!requireDownwardScroll || scrollDirection === 'down') {
            setIsInView(true);
            if (triggerOnce) {
              setHasTriggered(true);
            }
          }
        } else if (!isVisible && !triggerOnce) {
          setIsInView(false);
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, rootMargin, triggerOnce, requireDownwardScroll, scrollDirection, hasTriggered]);

  return { ref: elementRef, isInView, hasTriggered };
};

export default useDirectionalInView;
