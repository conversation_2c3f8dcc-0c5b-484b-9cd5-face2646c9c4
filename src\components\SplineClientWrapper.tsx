'use client';

import Spline from '@splinetool/react-spline/next';
import React, { useState, useCallback } from 'react';
import SplineErrorBoundary from './SplineErrorBoundary';

const SplineClientWrapper = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const onLoad = useCallback(() => {
    setIsLoading(false);
    setHasError(false);
  }, []);

  const onError = useCallback((error: any) => {
    console.warn('Spline scene failed to load:', error);
    setIsLoading(false);
    setHasError(true);
  }, []);

  return (
    <SplineErrorBoundary>
      <div style={{
        width: '100%',
        maxWidth: 340,
        minHeight: 400,
        height: 500,
        background: '#18122b',
        borderRadius: '1.5rem',
        boxShadow: '0 4px 32px #7c3aed33',
        border: '2px solid #655dbb44',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 30,
        position: 'relative'
      }}>
        {isLoading && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            color: '#a855f7',
            fontSize: '16px',
            fontWeight: 'bold'
          }}>
            Loading...
          </div>
        )}

        {hasError ? (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            color: '#ef4444',
            fontSize: '14px',
            textAlign: 'center'
          }}>
            <div>3D Model Unavailable</div>
            <div style={{ fontSize: '12px', marginTop: '4px', color: '#9ca3af' }}>
              Check connection
            </div>
          </div>
        ) : (
          <Spline
            scene="https://prod.spline.design/Ia5uIe3suHmUQXNu/scene.splinecode"
            style={{
              width: '100%',
              height: '100%',
              borderRadius: '1.5rem',
              background: 'transparent'
            }}
            onLoad={onLoad}
            onError={onError}
          />
        )}
      </div>
    </SplineErrorBoundary>
  );
};

export default SplineClientWrapper;