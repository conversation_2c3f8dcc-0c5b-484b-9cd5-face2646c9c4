"use client";
import React, { useState, useRef } from "react";
import emailjs from "@emailjs/browser";

export default function ContactMeModalButton() {
  const [open, setOpen] = useState(false);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("<PERSON> Ju<PERSON>, I’d love to collaborate with you on a project! Let’s connect and discuss the details.");
  const [loading, setLoading] = useState(false);
  const [toast, setToast] = useState<{ type: 'success' | 'error', msg: string } | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const isValidEmail = (email: string) => /.+@.+\..+/.test(email);

  const handleSend = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!isValidEmail(email)) {
      setToast({ type: 'error', msg: 'Please enter a valid email address.' });
      return;
    }
    setLoading(true);
    try {
      await emailjs.send(
        'service_sxkg0c8',
        'template_8uf6tkv',
        {
          name,
          user_email: email,
          message,
          time: new Date().toLocaleString(),
          reply_to: email,
        },
        'QbiMivJJwMgyBsr18'
      );
      setToast({ type: 'success', msg: 'Message sent successfully!' });
      setOpen(false);
      setEmail("");
      setName("");
      setMessage("Hi Jugal, I’d love to collaborate with you on a project! Let’s connect and discuss the details.");
    } catch (err) {
      setToast({ type: 'error', msg: 'Failed to send message. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    if (toast) {
      const timer = setTimeout(() => setToast(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [toast]);

  return (
    <>
      <button
        className="bg-white text-royal-purple hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition duration-300"
        onClick={() => setOpen(true)}
      >
        Contact Me
      </button>
      {open && (
        <div className="fixed inset-0 z-[99999] flex items-center justify-center bg-black/60 backdrop-blur-[8px]">
          <div
            className="relative z-[100000] w-full max-w-lg mx-4 glass-modal border border-royal-purple/30 rounded-3xl shadow-xl p-8 md:p-12"
            style={{
              background: 'rgba(30, 22, 60, 0.55)',
              backdropFilter: 'blur(18px)',
              WebkitBackdropFilter: 'blur(18px)',
              boxShadow: '0 0 24px 4px #a855f755, 0 0 48px 8px #6366f122',
            }}
            onClick={e => e.stopPropagation()}
          >
            <button
              className="absolute top-4 right-4 text-white/80 hover:text-white text-2xl focus:outline-none"
              aria-label="Close modal"
              onClick={() => setOpen(false)}
            >
              ×
            </button>
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-6 text-center">Let's Collaborate!</h2>
            <form onSubmit={handleSend} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-white/80 font-medium mb-2">Your Name</label>
                <input
                  id="name"
                  type="text"
                  className="w-full rounded-xl bg-black/40 border border-royal-purple/30 text-white p-4 focus:outline-none focus:ring-2 focus:ring-electric-indigo/60"
                  value={name}
                  onChange={e => setName(e.target.value)}
                  aria-label="Your Name"
                  required
                />
              </div>
              <div>
                <label htmlFor="message" className="block text-white/80 font-medium mb-2">Message</label>
                <textarea
                  ref={textareaRef}
                  id="message"
                  className="w-full min-h-[100px] rounded-xl bg-black/40 border border-royal-purple/30 text-white p-4 focus:outline-none focus:ring-2 focus:ring-electric-indigo/60 resize-none"
                  value={message}
                  onChange={e => setMessage(e.target.value)}
                  aria-label="Message"
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-white/80 font-medium mb-2">Your Email Address</label>
                <input
                  id="email"
                  type="email"
                  className="w-full rounded-xl bg-black/40 border border-royal-purple/30 text-white p-4 focus:outline-none focus:ring-2 focus:ring-electric-indigo/60"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  aria-label="Your Email Address"
                  required
                />
              </div>
              <button
                type="submit"
                className="w-full py-3 rounded-xl bg-gradient-to-r from-royal-purple to-electric-indigo text-white font-bold text-lg shadow-lg hover:scale-105 transition-all duration-300 flex items-center justify-center gap-3 disabled:opacity-60 disabled:cursor-not-allowed"
                disabled={loading}
                aria-busy={loading}
              >
                {loading ? (
                  <span className="loader border-2 border-t-2 border-t-white border-white/30 rounded-full w-5 h-5 animate-spin"></span>
                ) : (
                  'Send Message'
                )}
              </button>
            </form>
          </div>
        </div>
      )}
      {toast && (
        <div
          className={`fixed bottom-8 left-1/2 -translate-x-1/2 z-[99999] px-6 py-4 rounded-xl font-semibold text-white shadow-lg ${toast.type === 'success' ? 'bg-green-600' : 'bg-red-600'}`}
        >
          {toast.msg}
        </div>
      )}
    </>
  );
} 