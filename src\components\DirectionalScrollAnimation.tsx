'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useDirectionalInView } from '@/hooks/useDirectionalInView';

interface DirectionalScrollAnimationProps {
  children: React.ReactNode;
  animation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'slideRight' | 'scale' | 'custom';
  duration?: number;
  delay?: number;
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
  requireDownwardScroll?: boolean;
  customVariants?: {
    hidden: any;
    visible: any;
  };
  className?: string;
}

const DirectionalScrollAnimation: React.FC<DirectionalScrollAnimationProps> = ({
  children,
  animation = 'fadeIn',
  duration = 1.2,
  delay = 0,
  threshold = 0.1,
  rootMargin = '0px',
  triggerOnce = true,
  requireDownwardScroll = true,
  customVariants,
  className = ''
}) => {
  const { ref, isInView } = useDirectionalInView({
    threshold,
    rootMargin,
    triggerOnce,
    requireDownwardScroll
  });

  const getAnimationVariants = () => {
    if (animation === 'custom') {
      return customVariants || {
        hidden: { opacity: 0 },
        visible: { opacity: 1 }
      };
    }

    const variants = {
      fadeIn: {
        hidden: { opacity: 0 },
        visible: { opacity: 1 }
      },
      slideUp: {
        hidden: { opacity: 0, y: 30 },
        visible: { opacity: 1, y: 0 }
      },
      slideLeft: {
        hidden: { opacity: 0, x: 30 },
        visible: { opacity: 1, x: 0 }
      },
      slideRight: {
        hidden: { opacity: 0, x: -30 },
        visible: { opacity: 1, x: 0 }
      },
      scale: {
        hidden: { opacity: 0, scale: 0.95 },
        visible: { opacity: 1, scale: 1 }
      }
    };

    return variants[animation] || variants.fadeIn;
  };

  const animationVariants = getAnimationVariants();

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={animationVariants}
      transition={{
        duration,
        delay,
        ease: [0.25, 0.1, 0.25, 1]
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default DirectionalScrollAnimation;
