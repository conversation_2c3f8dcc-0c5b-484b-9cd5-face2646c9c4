'use client';

import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class SplineErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Only log non-Spline errors to avoid console spam
    const isSplineError = error.message.toLowerCase().includes('spline') ||
                         error.stack?.toLowerCase().includes('spline') ||
                         error.stack?.toLowerCase().includes('runtime');
    
    if (!isSplineError) {
      console.error('Error caught by SplineErrorBoundary:', error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      // Render fallback UI
      return this.props.fallback || (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: '400px',
          background: '#18122b',
          borderRadius: '1.5rem',
          border: '2px solid #655dbb44',
          color: '#a855f7',
          fontSize: '16px',
          textAlign: 'center',
          flexDirection: 'column'
        }}>
          <div style={{ marginBottom: '8px' }}>🎨</div>
          <div>3D Model Loading...</div>
          <div style={{ fontSize: '14px', color: '#9ca3af', marginTop: '4px' }}>
            Interactive content will appear here
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default SplineErrorBoundary;
