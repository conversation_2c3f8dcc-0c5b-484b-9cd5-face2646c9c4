@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;600;700&display=swap');
@import 'aos/dist/aos.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Modern futuristic color palette */
  --primary-bg: #0f0f1b;
  --section-bg-1: #131424;
  --section-bg-2: #1a1b2f;
  --accent-purple: #a855f7;
  --neon-violet: #7c3aed;
  --sky-blue: #38bdf8;
  --gradient-start: #a855f7;
  --gradient-end: #ec4899;
  --text-primary: #e5e7eb;
  --text-secondary: #9ca3af;
  --glass-outline: rgba(255, 255, 255, 0.08);
  --glow-purple: #a855f7;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--primary-bg);
  color: var(--text-primary);
  font-family: 'Poppins', 'Inter', sans-serif;
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--primary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-purple);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neon-violet);
}

/* Crisp glass effects without blur */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(168, 85, 247, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glass-card {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(168, 85, 247, 0.15);
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(168, 85, 247, 0.3);
  box-shadow:
    0 16px 64px rgba(168, 85, 247, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.glass-nav {
  background: rgba(15, 15, 27, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(168, 85, 247, 0.2);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, var(--accent-purple), var(--neon-violet));
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease-in-out infinite;
}

/* Neon glow effects */
.neon-glow {
  box-shadow:
    0 0 10px rgba(168, 85, 247, 0.5),
    0 0 20px rgba(168, 85, 247, 0.3),
    0 0 30px rgba(168, 85, 247, 0.1);
}

.neon-glow:hover {
  box-shadow:
    0 0 15px rgba(168, 85, 247, 0.7),
    0 0 30px rgba(168, 85, 247, 0.5),
    0 0 45px rgba(168, 85, 247, 0.3);
}

/* Landing page styles */
.landing-active {
  overflow: hidden;
  height: 100vh;
}

/* Smooth transitions */
.page-transition {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern futuristic utility classes */
.eki-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.eki-section {
  padding: 5rem 0;
  position: relative;
}

.eki-card {
  background: var(--section-bg-2);
  border: 1px solid var(--glass-outline);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.eki-card:hover {
  background: var(--section-bg-1);
  border-color: var(--accent-purple);
  transform: translateY(-5px);
  box-shadow: 0 0 20px var(--glow-purple);
}

.eki-button {
  background: linear-gradient(135deg, var(--accent-purple), var(--neon-violet));
  border: none;
  border-radius: 12px;
  padding: 0.75rem 2rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.eki-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 15px var(--glow-purple);
}

.eki-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.eki-button:hover::before {
  left: 100%;
}

/* Tab navigation styles */
.eki-tabs {
  display: flex;
  background: var(--section-bg-1);
  border-radius: 16px;
  padding: 0.75rem;
  border: 1px solid var(--glass-outline);
  gap: 0.5rem;
}

.eki-tab {
  flex: 1;
  padding: 1.25rem 2.5rem;
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 1rem;
  min-width: 160px;
}

.eki-tab.active {
  background: linear-gradient(135deg, var(--accent-purple), var(--neon-violet));
  color: white;
  box-shadow: 0 0 15px var(--glow-purple);
}

.eki-tab:not(.active):hover {
  background: var(--section-bg-2);
  color: var(--accent-purple);
}

/* 3D perspective utilities for enhanced animations */
.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

/* Enhanced card animations */
.eki-card {
  transform-origin: center center;
  backface-visibility: hidden;
}

/* Floating animation keyframes */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Pulse glow effect */
@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(124, 58, 237, 0.3); }
  50% { box-shadow: 0 0 20px rgba(124, 58, 237, 0.6), 0 0 30px rgba(124, 58, 237, 0.4); }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.cursor {
  display: inline-block;
  width: 2px; /* Increased width for better visibility */
  background-color: var(--accent-purple); /* Accent purple */
  animation: blink 1s step-start infinite;
  margin-left: 2px; /* Slight space between text and cursor */
}

@keyframes blink {
  50% { opacity: 0; }
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Tech Stack Card Styles - EKI Inspired */
.tech-card {
  background: rgba(26, 26, 64, 0.9);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.tech-card:hover {
  background: rgba(42, 42, 96, 0.8);
  border-color: rgba(168, 85, 247, 0.5);
  transform: translateY(-5px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(168, 85, 247, 0.3);
}

.tech-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tech-card:hover::before {
  opacity: 1;
}

/* Backend Developer Glow Effects - Lightened */
@keyframes backendGlow {
  0% {
    text-shadow: 0 0 6px rgba(168, 85, 247, 0.4), 0 0 12px rgba(147, 51, 234, 0.3), 0 0 18px rgba(124, 58, 237, 0.2);
  }
  50% {
    text-shadow: 0 0 8px rgba(168, 85, 247, 0.5), 0 0 16px rgba(147, 51, 234, 0.4), 0 0 24px rgba(124, 58, 237, 0.3), 0 0 32px rgba(109, 40, 217, 0.2);
  }
  100% {
    text-shadow: 0 0 6px rgba(168, 85, 247, 0.4), 0 0 12px rgba(147, 51, 234, 0.3), 0 0 18px rgba(124, 58, 237, 0.2);
  }
}

@keyframes developerGlow {
  0% {
    text-shadow: 0 0 8px rgba(168, 85, 247, 0.4), 0 0 15px rgba(147, 51, 234, 0.3), 0 0 22px rgba(124, 58, 237, 0.2);
  }
  50% {
    text-shadow: 0 0 10px rgba(168, 85, 247, 0.5), 0 0 20px rgba(147, 51, 234, 0.4), 0 0 30px rgba(124, 58, 237, 0.3), 0 0 40px rgba(109, 40, 217, 0.2);
  }
  100% {
    text-shadow: 0 0 8px rgba(168, 85, 247, 0.4), 0 0 15px rgba(147, 51, 234, 0.3), 0 0 22px rgba(124, 58, 237, 0.2);
  }
}

@keyframes typewriterGlow {
  0% {
    text-shadow: 0 0 4px rgba(139, 92, 246, 0.4), 0 0 8px rgba(124, 58, 237, 0.3);
  }
  50% {
    text-shadow: 0 0 6px rgba(139, 92, 246, 0.5), 0 0 12px rgba(124, 58, 237, 0.4), 0 0 18px rgba(109, 40, 217, 0.2);
  }
  100% {
    text-shadow: 0 0 4px rgba(139, 92, 246, 0.4), 0 0 8px rgba(124, 58, 237, 0.3);
  }
}

.backend-glow {
  animation: backendGlow 3s ease-in-out infinite;
}

.developer-glow {
  animation: developerGlow 3.5s ease-in-out infinite;
}

.typewriter-glow {
  text-shadow: 0 0 4px rgba(139, 92, 246, 0.4), 0 0 8px rgba(124, 58, 237, 0.3) !important;
  animation: typewriterGlow 2.5s ease-in-out infinite;
}

/* Button and Icon Glow Effects */
@keyframes buttonGlow {
  0% {
    text-shadow: 0 0 4px rgba(168, 85, 247, 0.4), 0 0 8px rgba(147, 51, 234, 0.3);
    box-shadow: 0 0 8px rgba(168, 85, 247, 0.2), 0 0 16px rgba(147, 51, 234, 0.1);
  }
  50% {
    text-shadow: 0 0 6px rgba(168, 85, 247, 0.5), 0 0 12px rgba(147, 51, 234, 0.4), 0 0 18px rgba(124, 58, 237, 0.2);
    box-shadow: 0 0 12px rgba(168, 85, 247, 0.3), 0 0 24px rgba(147, 51, 234, 0.2), 0 0 36px rgba(124, 58, 237, 0.1);
  }
  100% {
    text-shadow: 0 0 4px rgba(168, 85, 247, 0.4), 0 0 8px rgba(147, 51, 234, 0.3);
    box-shadow: 0 0 8px rgba(168, 85, 247, 0.2), 0 0 16px rgba(147, 51, 234, 0.1);
  }
}

@keyframes iconGlow {
  0% {
    text-shadow: 0 0 3px rgba(168, 85, 247, 0.4), 0 0 6px rgba(147, 51, 234, 0.3);
    filter: drop-shadow(0 0 4px rgba(168, 85, 247, 0.2));
  }
  50% {
    text-shadow: 0 0 5px rgba(168, 85, 247, 0.5), 0 0 10px rgba(147, 51, 234, 0.4), 0 0 15px rgba(124, 58, 237, 0.2);
    filter: drop-shadow(0 0 8px rgba(168, 85, 247, 0.3));
  }
  100% {
    text-shadow: 0 0 3px rgba(168, 85, 247, 0.4), 0 0 6px rgba(147, 51, 234, 0.3);
    filter: drop-shadow(0 0 4px rgba(168, 85, 247, 0.2));
  }
}

.button-glow {
  animation: buttonGlow 2.8s ease-in-out infinite;
}

.icon-glow {
  animation: iconGlow 2.2s ease-in-out infinite;
}

.social-icon-glow {
  animation: iconGlow 2.5s ease-in-out infinite;
}

/* Animated gradient border effect for buttons */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Hide the 'Built with Spline' badge from Spline viewer */
.spline-viewer__badge,
spline-viewer .spline-viewer__badge,
spline-viewer > div[style*="Built with Spline"],
spline-viewer .spline-watermark,
.spline-watermark {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Suppress Spline console warnings and errors */
spline-viewer,
.spline-viewer {
  --spline-console-level: none;
}

/* Additional Spline error suppression */
spline-viewer canvas {
  outline: none !important;
}

/* Hide any Spline error overlays */
spline-viewer .error-overlay,
.spline-error-overlay {
  display: none !important;
}

.glow-effect {
  box-shadow: 0 0 80px 20px rgba(124, 58, 237, 0.25), 0 0 120px 40px rgba(168, 85, 247, 0.15);
  border-radius: 2rem;
}
