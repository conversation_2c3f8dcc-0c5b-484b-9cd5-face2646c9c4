'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const ExperienceSection = () => {
  const [activeSection, setActiveSection] = useState('projects');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  // Add variants for content animation
  const contentVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
    exit: { opacity: 0, y: 20, transition: { duration: 0.3, ease: "easeIn" } }
  };

  const cardVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  // Portfolio showcase categories
  const showcaseCategories = [
    {
      id: "projects",
      icon: "💻",
      title: "Projects",
    },
    {
      id: "certificates",
      icon: "🏆",
      title: "Certificates",
    },
    {
      id: "techstack",
      icon: "⚡",
      title: "Tech Stack",
    }
  ];

  // Projects data
  const projects = [
    {
      title: "Aritmatika Solver",
      description: "Program ini dirancang untuk memecahkan masalah penggunaan sistem menyelesaikan soal-soal Aritmatika secara otomatis.",
      image: "/api/placeholder/300/200",
      technologies: ["Python", "Algorithm", "Math"],
      liveUrl: "#",
      githubUrl: "#"
    },
    {
      title: "AutoChat-Discord",
      description: "AutoChat adalah solusi otomatis untuk mengirim pesan ke saluran Discord secara terjadwal. Mengirim dapat:",
      image: "/api/placeholder/300/200",
      technologies: ["Discord.js", "Node.js", "JavaScript"],
      liveUrl: "#",
      githubUrl: "#"
    },
    {
      title: "Buku Catatan",
      description: "Buku Catatan adalah website yang memungkinkan pengguna untuk membuat, menyimpan, dan mengelola.",
      image: "/api/placeholder/300/200",
      technologies: ["HTML", "CSS", "JavaScript"],
      liveUrl: "#",
      githubUrl: "#"
    }
  ];

  // Certificates data
  const certificates = [
    {
      title: "JavaScript Fundamentals",
      issuer: "Dicoding",
      image: "/api/placeholder/300/400",
      date: "2023"
    },
    {
      title: "React Development",
      issuer: "Dicoding",
      image: "/api/placeholder/300/400",
      date: "2023"
    },
    {
      title: "Node.js Backend",
      issuer: "Dicoding",
      image: "/api/placeholder/300/400",
      date: "2023"
    },
    {
      title: "Web Programming",
      issuer: "Dicoding",
      image: "/api/placeholder/300/400",
      date: "2023"
    },
    {
      title: "Frontend Development",
      issuer: "Dicoding",
      image: "/api/placeholder/300/400",
      date: "2023"
    },
    {
      title: "Database Management",
      issuer: "Dicoding",
      image: "/api/placeholder/300/400",
      date: "2023"
    }
  ];

  // Tech stack data
  const techStack = [
    { name: "HTML", icon: "🌐", color: "from-[#00f0ff] to-[#a855f7]" },
    { name: "CSS", icon: "🎨", color: "from-[#facc15] to-[#00f0ff]" },
    { name: "JavaScript", icon: "⚡", color: "from-[#a855f7] to-[#facc15]" },
    { name: "Tailwind CSS", icon: "🌊", color: "from-[#00f0ff] to-[#a855f7]" },
    { name: "ReactJS", icon: "⚛️", color: "from-[#facc15] to-[#00f0ff]" },
    { name: "Vite", icon: "⚡", color: "from-[#a855f7] to-[#facc15]" },
    { name: "Node.JS", icon: "🟢", color: "from-[#00f0ff] to-[#a855f7]" },
    { name: "Bootstrap", icon: "🅱️", color: "from-[#facc15] to-[#00f0ff]" },
    { name: "Firebase", icon: "🔥", color: "from-[#a855f7] to-[#facc15]" },
    { name: "Material UI", icon: "🎯", color: "from-[#00f0ff] to-[#a855f7]" },
    { name: "Vercel", icon: "▲", color: "from-[#facc15] to-[#00f0ff]" },
    { name: "SweetAlert2", icon: "🍭", color: "from-[#a855f7] to-[#facc15]" }
  ];

  return (
    <section className="min-h-screen py-24 relative">
      <div className="container mx-auto px-8 md:px-12 lg:px-16 relative z-10">
        <motion.div
          className="max-w-7xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          {/* Section Header */}
          <motion.div className="text-left mb-20" variants={itemVariants}>
            <h2 className="text-4xl md:text-5xl font-bold mb-8">
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-royal-purple to-electric-indigo">
                Portfolio Showcase
              </span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl leading-relaxed">
              Explore my journey through projects, certifications, and technical expertise. Each section represents a milestone in my continuous learning path.
            </p>
          </motion.div>

          {/* Navigation Bar - jugal.my.id style */}
          <motion.div
            className="flex justify-start mb-20"
            variants={containerVariants}
          >
            <motion.div
              className="glass p-4 rounded-2xl backdrop-blur-md border border-royal-purple/20 shadow-2xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="flex gap-4">
                {showcaseCategories.map((category, index) => (
                  <motion.button
                    key={category.id}
                    className={`relative px-12 py-5 rounded-xl font-semibold transition-all duration-300 flex items-center gap-4 min-w-[160px] justify-center ${
                      activeSection === category.id
                        ? 'bg-gradient-to-r from-royal-purple to-electric-indigo text-white shadow-lg shadow-royal-purple/25'
                        : 'text-gray-300 hover:text-white hover:bg-white/5 border border-transparent hover:border-royal-purple/30'
                    }`}
                    onClick={() => setActiveSection(category.id)}
                    whileHover={{ scale: 1.02, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                  >
                    <span className="text-2xl">{category.icon}</span>
                    <span className="font-medium text-base">{category.title}</span>
                    {activeSection === category.id && (
                      <motion.div
                        className="absolute inset-0 rounded-xl bg-gradient-to-r from-royal-purple to-electric-indigo"
                        layoutId="activeTab"
                        style={{ zIndex: -1 }}
                        transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                      />
                    )}
                  </motion.button>
                ))}
              </div>
            </motion.div>
          </motion.div>

          {/* Dynamic Content Based on Active Section with Animation */}
          <AnimatePresence mode="wait">
            <motion.div
              key={activeSection}
              variants={contentVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              className="mb-20"
            >
              {activeSection === 'projects' && (
                <div>
                  <div className="grid md:grid-cols-3 gap-10 mb-16">
                    {projects.map((project, index) => (
                      <motion.div
                        key={index}
                        className="glass p-8 rounded-xl group text-left"
                        variants={cardVariants}
                        whileHover={{ y: -5 }}
                      >
                        <div className="w-full h-56 bg-gradient-to-br from-deep-violet/20 to-royal-purple/20 rounded-lg mb-6 flex items-center justify-center text-4xl">
                          💻
                        </div>
                        <h4 className="text-xl font-bold text-white-smoke mb-4 group-hover:text-electric-indigo transition-colors">
                          {project.title}
                        </h4>
                        <p className="text-gray-400 text-base mb-6 leading-relaxed">
                          {project.description}
                        </p>
                        <div className="flex flex-wrap gap-3 mb-6">
                          {project.technologies.map((tech) => (
                            <span
                              key={tech}
                              className="px-3 py-1.5 bg-deep-violet/30 text-white-smoke rounded text-sm border border-royal-purple/30"
                            >
                              {tech}
                            </span>
                          ))}
                        </div>
                        <div className="flex gap-4">
                          <motion.button 
                            className="px-4 py-2 bg-royal-purple text-white text-sm rounded hover:bg-electric-indigo transition-colors"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            Live Demo
                          </motion.button>
                          <motion.button 
                            className="px-4 py-2 glass text-white-smoke text-sm rounded hover:bg-white/20 transition-colors"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            Details
                          </motion.button>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {activeSection === 'certificates' && (
                <div>
                  <div className="grid md:grid-cols-3 gap-10 mb-16">
                    {certificates.map((cert, index) => (
                      <motion.div
                        key={index}
                        className="glass p-8 rounded-xl group text-left"
                        variants={cardVariants}
                        whileHover={{ y: -5 }}
                      >
                        <div className="w-full h-72 bg-gradient-to-br from-deep-violet/20 to-royal-purple/20 rounded-lg mb-6 flex items-center justify-center">
                          <div className="text-center">
                            <div className="text-5xl mb-4">🏆</div>
                            <div className="text-base text-gray-400">Certificate</div>
                          </div>
                        </div>
                        <h4 className="text-xl font-bold text-white-smoke mb-3 group-hover:text-electric-indigo transition-colors">
                          {cert.title}
                        </h4>
                        <p className="text-gray-400 text-base mb-3">
                          Issued by {cert.issuer}
                        </p>
                        <p className="text-gray-500 text-sm">
                          {cert.date}
                        </p>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {activeSection === 'techstack' && (
                <div className="relative">
                  {/* Background gradient and dot pattern */}
                  <div className="absolute inset-0 bg-[#0f0f25] rounded-3xl opacity-50" />
                  <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-10" />
                  
                  {/* Tech stack grid */}
                  <div className="relative grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6 p-8">
                    {techStack.map((tech, index) => (
                      <motion.div
                        key={index}
                        className="group relative"
                        variants={cardVariants}
                        whileHover={{ y: -5, scale: 1.05 }}
                        transition={{ duration: 0.3 }}
                      >
                        {/* Card container */}
                        <div className="relative bg-[#1a1a40] rounded-2xl p-6 backdrop-blur-sm border border-white/10 overflow-hidden">
                          {/* Gradient border effect */}
                          <div className="absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                               style={{ backgroundImage: `linear-gradient(to right, ${tech.color.split(' ')[1]}, ${tech.color.split(' ')[3]})` }} />
                          
                          {/* Content */}
                          <div className="relative z-10 flex flex-col items-center">
                            <div className="text-4xl mb-4 transform group-hover:scale-110 transition-transform duration-300">
                              {tech.icon}
                            </div>
                            <h4 className="text-white font-bold text-sm tracking-wider uppercase group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r transition-all duration-300"
                                style={{ backgroundImage: `linear-gradient(to right, ${tech.color.split(' ')[1]}, ${tech.color.split(' ')[3]})` }}>
                              {tech.name}
                            </h4>
                          </div>
                          
                          {/* Glow effect */}
                          <div className="absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-300"
                               style={{ backgroundImage: `linear-gradient(to right, ${tech.color.split(' ')[1]}, ${tech.color.split(' ')[3]})` }} />
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          </AnimatePresence>

          {/* Call to Action */}
          <motion.div
            className="text-left"
            variants={itemVariants}
          >
            <motion.button
              className="px-10 py-5 bg-gradient-to-r from-royal-purple to-electric-indigo text-white font-semibold rounded-lg hover:shadow-lg hover:shadow-royal-purple/25 transition-all duration-300 mr-6"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Contact Me 🚀
            </motion.button>
            <motion.button
              className="px-10 py-5 glass text-white-smoke font-semibold rounded-lg hover:bg-white/20 transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Download Resume 📄
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default ExperienceSection;
