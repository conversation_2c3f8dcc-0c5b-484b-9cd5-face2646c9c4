// Spline error handling utilities
export const suppressSplineConsoleErrors = () => {
  // Store original console methods
  const originalError = console.error;
  const originalWarn = console.warn;
  const originalLog = console.log;

  // Override console methods to filter Spline-related errors
  console.error = (...args: any[]) => {
    const message = args.join(' ');
    
    // Filter out common Spline errors that don't affect functionality
    const splineErrorPatterns = [
      'spline',
      'splinetool',
      'runtime',
      'buildTimeline',
      'console.error',
      'handleConsoleError',
      'createConsoleError',
      'Missing property',
      'eval',
      'mg.buildTimeline'
    ];

    const shouldSuppress = splineErrorPatterns.some(pattern => 
      message.toLowerCase().includes(pattern.toLowerCase())
    );

    if (!shouldSuppress) {
      originalError.apply(console, args);
    }
  };

  console.warn = (...args: any[]) => {
    const message = args.join(' ');
    
    const splineWarningPatterns = [
      'spline',
      'splinetool',
      'runtime',
      'scene failed to load'
    ];

    const shouldSuppress = splineWarningPatterns.some(pattern => 
      message.toLowerCase().includes(pattern.toLowerCase())
    );

    if (!shouldSuppress) {
      originalWarn.apply(console, args);
    }
  };

  // Return cleanup function
  return () => {
    console.error = originalError;
    console.warn = originalWarn;
    console.log = originalLog;
  };
};

// Initialize error suppression
export const initSplineErrorSuppression = () => {
  if (typeof window !== 'undefined') {
    // Suppress errors on window load
    window.addEventListener('load', suppressSplineConsoleErrors);
    
    // Also suppress immediately
    suppressSplineConsoleErrors();
  }
};
