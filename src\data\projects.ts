export interface Project {
  id: string;
  title: string;
  description: string;
  cardSummary: string; // concise summary for flexbox
  tech: string[];
  highlights: string[];
  category: string[];
  image: string;
  screenshot: string;
  link: string;
  github: string;
  teamSize?: string;
  duration?: string;
  role?: string;
}

export const projects: Project[] = [
  {
    id: "ai-interview-coach",
    title: "AI Interview Coach",
    description: "This innovative AI Interview Coach simulates realistic HR and technical interview scenarios by leveraging the Gemini Flash 2.0 API for dynamic question-and-answer interactions. It provides crucial, NLP-based feedback on a user's tone and fluency, helping individuals refine their communication skills. A key new feature includes resume ATS and job profile matching checks using OCR, enhancing its utility for placement preparation. The project has been effectively utilized by peers for comprehensive placement preparation, showcasing its practical utility in a real-world context. This tool streamlines the interview practice process, offering targeted insights for improvement.",
    cardSummary: "Practice HR and tech interviews with real-time Q&A and instant NLP feedback on your communication skills.",
    tech: ["Python", "Gemini API", "SpeechRecognition", "pyttsx3","OCR"],
    highlights: [
      "Simulates HR and tech interviews with real-time Q&A.",
      "Provides NLP-based feedback on tone and fluency.",
      "Used by peers for placement preparation.",
      "Includes resume ATS and job profile matching check using OCR"
    ],
    category: ["AI", "Automation"],
    image: "🤖",
    screenshot: "/images/ai-interview-coach.png",
    link: "#",
    github: "#",
    teamSize: "2 Person",
    duration: "2 Months",
    role: "Backend Developer"
  },
  {
    id: "stock-market-predictor",
    title: "Stock Market Predictor",
    description: "This Stock Market Predictor project is designed to forecast stock market trends by employing key technical indicators such as Simple Moving Average (SMA) and Relative Strength Index (RSI). It integrates seamlessly with real-time yfinance data to ensure the most current market information is used for analysis. The project also incorporates robust data visualizations, enabling users to easily interpret complex market movements and make informed decisions based on the predicted trends.",
    cardSummary: "Predict market trends using SMA, RSI, and real-time financial data with interactive visualizations.",
    tech: ["Python", "scikit-learn", "yfinance"],
    highlights: [
      "Forecasts stock market trends using SMA and RSI indicators.",
      "Utilizes real-time yfinance data for up-to-date information.",
      "Provides visualizations for better understanding of market trends."
    ],
    category: ["AI", "Automation"],
    image: "📈",
    screenshot: "/images/stock-market-predictor.png",
    link: "#",
    github: "https://github.com/Jugalsoni18/ML_StockPricePredictor"
  },
  {
    id: "privychat-secure-chat-app",
    title: "PrivyChat – Secure Chat App",
    description: "PrivyChat is a secure, real-time encrypted messaging application specifically engineered for private study groups, ensuring confidential communication. It leverages Firebase Authentication for robust user management and Firebase Database for scalable, secure data storage and deployment. The application prioritizes privacy and real-time interaction, making it an ideal platform for collaborative learning environments where data security is paramount. Tailwind CSS is used for a streamlined and responsive user interface.",
    cardSummary: "Encrypted, real-time messaging for private groups with Firebase authentication and secure data.",
    tech: ["Next.js", "Firebase", "Tailwind CSS", "CryptoJS"],
    highlights: [
      "Offers real-time encrypted messaging for secure communication.",
      "Designed specifically for private study groups",
      "Utilizes Firebase Auth & DB for deployment and secure data handling."
    ],
    category: ["Web", "Backend"],
    image: "🔒",
    screenshot: "/images/privychat.png",
    link: "#",
    github: "#"
  },
  {
    id: "jarvis-ai-assistant",
    title: "Jarvis AI Assistant",
    description: "The Jarvis AI Assistant is a sophisticated voice-controlled system designed for automation and smart control. It intelligently handles a variety of tasks, from launching applications to fetching news, by processing voice commands using the Google Cloud Speech-to-Text API. The assistant is powered by TinyLlama for efficient natural language processing, offering a seamless and hands-free user experience for daily digital interactions and management.",
    cardSummary: "Voice-controlled assistant for automation, app launching, and real-time news using AI APIs.",
    tech: ["Python", "Google Cloud Speech-to-Text API", "Gemini API", "TinyLlama"],
    highlights: [
      "Voice assistant for automation and smart control.",
      "Utilizes Google Cloud Speech-to-Text API for processing voice commands.",
      "Handles tasks like launching apps and fetching news."
    ],
    category: ["AI", "Automation"],
    image: "🗣️",
    screenshot: "/images/jarvis-ai.png",
    link: "#",
    github: "https://github.com/Jugalsoni18/Jarvis-AI-Assistant"
  },
  {
    id: "shopeasy-upi-payment-gateway",
    title: "ShopEasy – UPI Payment Gateway",
    description: "ShopEasy functions as a mock e-commerce platform that implements real-time UPI payment transactions through the Razorpay API. This project includes a comprehensive admin dashboard, providing functionalities for managing sales and overseeing operations. Additionally, it offers detailed analytics, enabling the tracking and analysis of transaction data to gain insights into business performance and customer behavior within a simulated e-commerce environment.",
    cardSummary: "Mock ecommerce platform with real-time UPI payments, admin dashboard, and analytics.",
    tech: ["Node.js", "PostgreSQL", "Razorpay API"],
    highlights: [
      "Facilitates real-time Razorpay-based transactions.",
      "Includes an admin dashboard for managing operations.",
      "Provides analytics for insights into sales and transactions."
    ],
    category: ["Web", "Backend", "Automation"],
    image: "🛒",
    screenshot: "/images/shopeasy.png",
    link: "#",
    github: "https://github.com/Jugalsoni18/payment-gateway"
  }
]; 