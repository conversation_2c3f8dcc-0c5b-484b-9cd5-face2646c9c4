import { notFound } from 'next/navigation';
import { projects } from '@/data/projects';
import { FaGithub, FaLinkedin, FaBolt, FaMobileAlt, FaShieldAlt, FaSyncAlt, FaReact, FaNodeJs, FaDatabase, FaJs, FaCss3Alt, FaSearch, FaPencilRuler, FaCode, FaBug, FaCheckCircle, FaExclamationCircle, FaExternalLinkAlt, FaArrowRight, FaEnvelope, FaPhoneAlt, FaMapMarkerAlt, FaInstagram, FaTwitter, FaMicrophone, FaLock, FaRobot } from 'react-icons/fa';
import Link from 'next/link';
import GlowingOrbsBackground from '@/components/GlowingOrbsBackground';
import ProjectCtaSection from '@/components/ProjectCtaSection';

export default async function ProjectDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const project = projects.find((p) => p.id === id);
  if (!project) return notFound();

  // Map tech to icons/colors (customize as needed)
  const techIconMap: Record<string, JSX.Element> = {
    'React': <FaReact className="mr-2 text-cyan-400 text-2xl" />, 'Next.js': <FaReact className="mr-2 text-cyan-400 text-2xl" />,
    'Node.js': <FaNodeJs className="mr-2 text-green-400 text-2xl" />,
    'MongoDB': <FaDatabase className="mr-2 text-green-300 text-2xl" />,
    'JavaScript': <FaJs className="mr-2 text-yellow-300 text-2xl" />,
    'Tailwind CSS': <FaCss3Alt className="mr-2 text-blue-400 text-2xl" />,
    'Firebase': <FaDatabase className="mr-2 text-yellow-400 text-2xl" />,
    'Python': <FaCode className="mr-2 text-blue-300 text-2xl" />,
    'Gemini API': <FaBolt className="mr-2 text-purple-400 text-2xl" />,
    'SpeechRecognition': <FaMicrophone className="mr-2 text-pink-400 text-2xl" />,
    'pyttsx3': <FaMicrophone className="mr-2 text-pink-400 text-2xl" />,
    'scikit-learn': <FaCode className="mr-2 text-orange-400 text-2xl" />,
    'yfinance': <FaCode className="mr-2 text-green-400 text-2xl" />,
    'CryptoJS': <FaLock className="mr-2 text-purple-400 text-2xl" />,
    'PostgreSQL': <FaDatabase className="mr-2 text-blue-400 text-2xl" />,
    'Razorpay API': <FaBolt className="mr-2 text-indigo-400 text-2xl" />,
    'Google Cloud Speech-to-Text API': <FaMicrophone className="mr-2 text-blue-400 text-2xl" />,
    'TinyLlama': <FaRobot className="mr-2 text-pink-400 text-2xl" />
  };

  return (
    <>
      <GlowingOrbsBackground />
      {/* Invisible anchors for orb animation */}
      <div id="hero-section" style={{ position: 'absolute', top: 0, height: 1, width: 1, pointerEvents: 'none' }} />
      <div id="about-section" style={{ position: 'absolute', top: '25vh', height: 1, width: 1, pointerEvents: 'none' }} />
      <div id="portfolio-section" style={{ position: 'absolute', top: '50vh', height: 1, width: 1, pointerEvents: 'none' }} />
      <div id="contact-section" style={{ position: 'absolute', top: '75vh', height: 1, width: 1, pointerEvents: 'none' }} />
      <div className="min-h-screen flex flex-col bg-gradient-to-br from-rich-black via-black/90 to-deep-violet/90 text-white-smoke font-sans">
      {/* Header */}
      <header className="pt-32 pb-20 text-center">
        <h1 className="text-6xl md:text-7xl font-extrabold mb-6 backend-glow">{project.title}</h1>
        <p className="text-2xl md:text-3xl text-electric-indigo max-w-3xl mx-auto mb-8">{project.cardSummary}</p>
        {/* Social Icons */}
        <div className="flex justify-center gap-12 mt-10">
          <a href={project.github} target="_blank" rel="noopener noreferrer" className="w-16 h-16 flex items-center justify-center rounded-full border-2 border-royal-purple/60 bg-black/60 neon-glow shadow-[0_0_24px_#a855f7,0_0_48px_#9333ea] hover:shadow-[0_0_48px_#a855f7,0_0_96px_#9333ea] transition-all duration-300 group">
            <FaGithub className="w-9 h-9 text-white group-hover:scale-110 transition-transform" />
          </a>
          <a href="#" target="_blank" rel="noopener noreferrer" className="w-16 h-16 flex items-center justify-center rounded-full border-2 border-royal-purple/60 bg-black/60 neon-glow shadow-[0_0_24px_#a855f7,0_0_48px_#9333ea] hover:shadow-[0_0_48px_#a855f7,0_0_96px_#9333ea] transition-all duration-300 group">
            <FaLinkedin className="w-9 h-9 text-white group-hover:scale-110 transition-transform" />
          </a>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 max-w-[110rem] mx-auto px-12 sm:px-20 lg:px-32 py-16 md:py-24">
        <div className="grid grid-cols-1 lg:grid-cols-[2fr_1.1fr] gap-24">
          {/* Main Content */}
          <div className="lg:col-span-1">
            {/* Project Overview */}
            <div className="mb-20">
              <h2 className="text-4xl font-bold text-white mb-10">Project Overview</h2>
              <div className="bg-black/60 rounded-3xl neon-glow border-2 border-royal-purple/40 shadow-2xl overflow-hidden project-image mb-12 flex items-center justify-center min-h-[480px] w-full max-w-[900px] mx-auto">
                <img src={project.screenshot} alt="Project Screenshot" className="w-full h-auto object-contain max-h-[480px]" />
              </div>
              <p className="text-2xl text-white/80 mb-8 leading-relaxed max-w-[900px] mx-auto">{project.description}</p>
            </div>

            {/* Key Features */}
            <div className="mb-20">
              <h2 className="text-4xl font-bold text-white mb-10">Key Features</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
                {project.highlights.map((feature, i) => (
                  <div key={i} className="bg-black/60 p-12 rounded-2xl neon-glow border-2 border-royal-purple/30 shadow-xl hover:shadow-purple-500/30 transition w-full max-w-[500px] mx-auto">
                    <div className="mb-5 text-electric-indigo text-4xl"><FaBolt /></div>
                    <h3 className="text-2xl font-semibold mb-3 text-white-smoke">{feature.split('.')[0]}</h3>
                    <p className="text-lg text-white/70">{feature}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-black/60 rounded-3xl neon-glow border-2 border-royal-purple/40 shadow-2xl p-12 sticky top-10 w-full max-w-[420px] mx-auto">
              <h2 className="text-3xl font-bold text-white mb-10">Project Details</h2>
              {/* Technologies Used */}
              <div className="mb-10">
                <h3 className="text-2xl font-semibold mb-5">Technologies Used</h3>
                <div className="flex flex-wrap gap-4">
                  {project.tech.map((tech, i) => (
                    <span key={i} className="tech-icon bg-royal-purple/20 text-electric-indigo px-6 py-3 rounded-full text-lg flex items-center border border-royal-purple/40">
                      {techIconMap[tech] || <FaCode className="mr-2 text-2xl" />} {tech}
                    </span>
                  ))}
                </div>
              </div>
              {/* Project Info */}
              <div className="mb-10">
                <h3 className="text-2xl font-semibold mb-5">Project Info</h3>
                <div className="space-y-5 text-lg">
                  <div className="flex justify-between">
                    <span className="text-white/70">Status</span>
                    <span className="font-bold text-white">Completed</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Duration</span>
                    <span className="font-bold text-white">{project.duration || '1 Month'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Team Size</span>
                    <span className="font-bold text-white">{project.teamSize || '1 Person'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">My Role</span>
                    <span className="font-bold text-white">{project.role || 'Full-stack Developer'}</span>
                  </div>
                </div>
              </div>
              {/* Source Code */}
              <div>
                <h3 className="text-2xl font-semibold mb-5">Source Code</h3>
                <a href={project.github} target="_blank" rel="noopener noreferrer" className="flex items-center justify-between bg-black/40 hover:bg-royal-purple/20 p-5 rounded-lg transition border border-royal-purple/30 text-lg font-semibold">
                  <span>View Repository</span>
                  <FaArrowRight className="text-electric-indigo text-2xl" />
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Challenges Section */}
        <section className="py-12 md:py-16">
          <h2 className="text-3xl font-bold text-white mb-12">Challenges & Solutions</h2>
          <div className="bg-black/60 rounded-xl shadow-md overflow-hidden">
            <div className="md:flex">
              <div className="md:w-1/2 p-8 md:p-12 bg-electric-indigo/10">
                <h3 className="text-2xl font-bold text-white mb-6">Challenges Faced</h3>
                <ul className="space-y-4">
                  {[
                    { icon: <FaExclamationCircle className="text-electric-indigo" />, title: 'Performance Optimization', desc: 'Initial load times were slower than expected on mobile devices.' },
                    { icon: <FaExclamationCircle className="text-electric-indigo" />, title: 'Data Synchronization', desc: 'Real-time updates across multiple clients presented consistency issues.' },
                    { icon: <FaExclamationCircle className="text-electric-indigo" />, title: 'Cross-browser Compatibility', desc: 'Certain features behaved differently across browsers.' },
                  ].map((item, i) => (
                    <li key={i} className="flex items-start">
                      <div className="flex-shrink-0 mt-1 mr-3">{item.icon}</div>
                      <div>
                        <h4 className="font-semibold">{item.title}</h4>
                        <p className="text-white/70">{item.desc}</p>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="md:w-1/2 p-8 md:p-12 bg-black/80">
                <h3 className="text-2xl font-bold text-white mb-6">Solutions Implemented</h3>
                <ul className="space-y-4">
                  {[
                    { icon: <FaCheckCircle className="text-green-400" />, title: 'Code Splitting', desc: 'Implemented lazy loading and optimized asset delivery.' },
                    { icon: <FaCheckCircle className="text-green-400" />, title: 'WebSockets', desc: 'Used Socket.IO for efficient real-time communication.' },
                    { icon: <FaCheckCircle className="text-green-400" />, title: 'Feature Detection', desc: 'Added polyfills and fallbacks for inconsistent browser APIs.' },
                  ].map((item, i) => (
                    <li key={i} className="flex items-start">
                      <div className="flex-shrink-0 mt-1 mr-3">{item.icon}</div>
                      <div>
                        <h4 className="font-semibold">{item.title}</h4>
                        <p className="text-white/70">{item.desc}</p>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Place CTA directly above the footer, full width, no extra margin */}
      <ProjectCtaSection />

      <footer className="w-full bg-black/90 text-white/60 py-10 px-4 border-t border-royal-purple/30 shadow-[0_-2px_24px_#a855f7,0_-4px_48px_#9333ea] mt-auto">
        <div className="max-w-[95vw] mx-auto flex flex-col md:flex-row md:justify-between gap-12 md:gap-32 lg:gap-44 xl:gap-56 text-lg md:text-xl lg:text-xl">
          <div>
            <h3 className="text-white text-lg font-semibold mb-4 lg:text-2xl xl:text-3xl">Jugal Soni's Portfolio</h3>
            <p className="mb-4 lg:text-xl xl:text-2xl">Creating innovative digital experiences with cutting-edge technologies.</p>
            <div className="flex space-x-4">
              <a href="https://github.com/Jugalsoni18" target="_blank" rel="noopener noreferrer" className="text-white/60 hover:text-white transition">
                <FaGithub className="text-3xl" />
              </a>
              <a href="https://www.linkedin.com/in/jugal-soni-8bb797308?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app" target="_blank" rel="noopener noreferrer" className="text-white/60 hover:text-white transition">
                <FaLinkedin className="text-3xl" />
              </a>
            </div>
          </div>
          <div>
            <h3 className="text-white text-lg font-semibold mb-4 lg:text-2xl xl:text-3xl">Quick Links</h3>
            <ul className="space-y-2 lg:space-y-4 xl:space-y-6">
              <li><a href="#" className="hover:text-white transition">Home</a></li>
              <li><a href="#" className="hover:text-white transition">Projects</a></li>
              <li><a href="#" className="hover:text-white transition">About</a></li>
              <li><a href="#" className="hover:text-white transition">Contact</a></li>
            </ul>
          </div>
          <div>
            <h3 className="text-white text-lg font-semibold mb-4 lg:text-2xl xl:text-3xl">Services</h3>
            <ul className="space-y-2 lg:space-y-4 xl:space-y-6">
              <li><a href="#" className="hover:text-white transition">AI-Powered Web Development</a></li>
              <li><a href="#" className="hover:text-white transition">Full-Stack Development</a></li>
              <li><a href="#" className="hover:text-white transition">AI & Machine Learning Solutions</a></li>
              <li><a href="#" className="hover:text-white transition">Chatbot & Voice Assistant Development</a></li>
              <li><a href="#" className="hover:text-white transition">Data Science & Automation</a></li>
            </ul>
          </div>
          <div>
            <h3 className="text-white text-lg font-semibold mb-4 lg:text-2xl xl:text-3xl">Contact</h3>
            <ul className="space-y-2 lg:space-y-4 xl:space-y-6">
              <li className="flex items-start">
                <FaEnvelope className="mr-2 mt-1" />
                <span><EMAIL></span>
              </li>
              <li className="flex items-start">
                <FaPhoneAlt className="mr-2 mt-1" />
                <span>9054714583</span>
              </li>
              <li className="flex items-start">
                <FaMapMarkerAlt className="mr-2 mt-1" />
                <span>Vadodara, Gujarat</span>
              </li>
            </ul>
          </div>
        </div>
        <div className="border-t border-white/10 mt-4 pt-0 text-center">
          <p className="text-xl my-2">&copy; 2025 Jugal Soni. All rights reserved.</p>
        </div>
      </footer>
    </div>
    </>
  );
} 