'use client';

import React, { useEffect, useState, useRef, RefObject } from "react";

// Orb type definition
interface OrbConfig {
  color: string;
  size: number;
  top: string;
  side: "left" | "right";
}

// Section state type
type SectionState = "above" | "in" | "below";

// Orb definitions for each section
const orbConfigs: Record<string, OrbConfig[]> = {
  hero: [
    { color: "#AE67FA", size: 520, top: "10%", side: "left" },
    { color: "#7B61FF", size: 340, top: "60%", side: "left" },
    { color: "#B667FF", size: 260, top: "30%", side: "left" },
  ],
  about: [
    { color: "#AE67FA", size: 420, top: "40%", side: "right" },
    { color: "#FB8FBF", size: 220, top: "70%", side: "right" },
    { color: "#66D9EF", size: 180, top: "20%", side: "right" },
  ],
  portfolio: [
    { color: "#F5EFFF", size: 400, top: "20%", side: "left" }, // very light purple
    { color: "#B8F7FF", size: 320, top: "60%", side: "left" }, // light blue
    { color: "#FFF6B8", size: 200, top: "40%", side: "left" }, // light yellow
  ],
  contact: [
    { color: "#AE67FA", size: 400, top: "20%", side: "left" },
    { color: "#B667FF", size: 320, top: "60%", side: "left" },
    { color: "#66D9EF", size: 220, top: "40%", side: "right" },
    { color: "#FB8FBF", size: 180, top: "70%", side: "right" },
  ],
};

const sectionIds = ["hero-section", "about-section", "portfolio-section", "contact-section"] as const;
type SectionId = typeof sectionIds[number];

const sectionMap: Record<SectionId, keyof typeof orbConfigs> = {
  "hero-section": "hero",
  "about-section": "about",
  "portfolio-section": "portfolio",
  "contact-section": "contact",
};

function getSectionState(
  sectionId: SectionId,
  idx: number,
  totalSections: number,
  isAtTop: boolean,
  isAtBottom: boolean
): SectionState {
  const el = document.getElementById(sectionId);
  if (!el) return "above";
  const rect = el.getBoundingClientRect();
  // Special case for first section (hero)
  if ((idx === 0 && isAtTop) || (idx === 0 && rect.top < window.innerHeight && rect.bottom > 0)) return "in";
  // Special case for last section (contact)
  if (
    idx === totalSections - 1 &&
    (
      // Start animating in as soon as the top of the contact section is within 80% of the viewport height
      rect.top < window.innerHeight * 0.8 &&
      rect.bottom > 0
    )
  ) return "in";
  if (rect.bottom < 0) return "above";
  if (rect.top > window.innerHeight) return "below";
  return "in";
}

// Add prop type for sectionRef
interface OrbsBackgroundProps {
  sectionRef?: RefObject<HTMLDivElement>;
}

export default function OrbsBackground({ sectionRef }: OrbsBackgroundProps) {
  const [sectionStates, setSectionStates] = useState<Record<SectionId, SectionState>>(
    sectionIds.reduce((acc, id) => ({ ...acc, [id]: "above" }), {} as Record<SectionId, SectionState>)
  );
  const [scroll, setScroll] = useState(0);
  const ticking = useRef(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      if (!ticking.current) {
        window.requestAnimationFrame(() => {
          const y = window.scrollY;
          const h = document.body.scrollHeight - window.innerHeight;
          setScroll(h > 0 ? y / h : 0);

          const isAtTop = window.scrollY === 0;
          const isAtBottom = Math.abs(window.innerHeight + window.scrollY - document.body.scrollHeight) < 2;
          const newStates: Record<SectionId, SectionState> = {} as Record<SectionId, SectionState>;
          for (let idx = 0; idx < sectionIds.length; idx++) {
            const id = sectionIds[idx];
            newStates[id] = getSectionState(id, idx, sectionIds.length, isAtTop, isAtBottom);
          }
          setSectionStates(newStates);
          ticking.current = false;
        });
        ticking.current = true;
      }
    };
    window.addEventListener("scroll", handleScroll, { passive: true });
    handleScroll();
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Animation settings for each section
  const getOrbAnimation = (
    section: keyof typeof orbConfigs,
    orb: OrbConfig,
    i: number,
    scroll: number
  ) => {
    let base = 260;
    let drift = 0;
    let verticalDrift = 0;
    let opacity;
    if (section === "portfolio") {
      opacity = 0.9 + 0.18 * Math.cos(i); // Much brighter for project pages
    } else {
      opacity = 0.6 + 0.18 * Math.cos(i); // Default for other sections
    }
    let transition =
      "left 2.2s cubic-bezier(.4,0,.2,1), right 2.2s cubic-bezier(.4,0,.2,1), top 2.2s cubic-bezier(.4,0,.2,1), opacity 2.2s cubic-bezier(.4,0,.2,1)";

    if (section === "hero") {
      // Subtle upward motion, left orbs
      drift = Math.sin(scroll * Math.PI + i) * 80;
      verticalDrift = Math.cos(scroll * Math.PI + i) * 18; // subtle up
      transition =
        "left 1.8s cubic-bezier(.4,0,.2,1), top 1.8s cubic-bezier(.4,0,.2,1), opacity 1.8s cubic-bezier(.4,0,.2,1)";
    } else if (section === "about") {
      // Orbs float in from right, more pronounced drift
      drift = Math.sin(scroll * Math.PI + i) * 120;
      verticalDrift = Math.cos(scroll * Math.PI + i) * 12;
      transition =
        "right 2.2s cubic-bezier(.4,0,.2,1), top 2.2s cubic-bezier(.4,0,.2,1), opacity 2.2s cubic-bezier(.4,0,.2,1)";
    } else if (section === "portfolio") {
      // Calm, ambient left-side motion
      drift = Math.sin(scroll * Math.PI + i) * 60;
      verticalDrift = Math.cos(scroll * Math.PI + i) * 8;
      transition =
        "left 2.4s cubic-bezier(.4,0,.2,1), top 2.4s cubic-bezier(.4,0,.2,1), opacity 2.4s cubic-bezier(.4,0,.2,1)";
    } else if (section === "contact") {
      // Slower, symmetrical, both sides
      drift = Math.sin(scroll * 0.5 * Math.PI + i) * 40;
      verticalDrift = Math.cos(scroll * 0.5 * Math.PI + i) * 6;
      transition =
        "left 3.2s cubic-bezier(.4,0,.2,1), right 3.2s cubic-bezier(.4,0,.2,1), top 3.2s cubic-bezier(.4,0,.2,1), opacity 3.2s cubic-bezier(.4,0,.2,1)";
    }
    return { drift, verticalDrift, opacity, transition };
  };

  return (
    <div className="fixed inset-0 -z-10 pointer-events-none">
      {mounted && sectionIds.map((id) => {
        const state = sectionStates[id];
        const section = sectionMap[id];
        const orbs = orbConfigs[section];
        return orbs.map((orb, i) => {
          const { drift, verticalDrift, opacity: baseOpacity, transition } = getOrbAnimation(section, orb, i, scroll);
          let pos = 260 + drift;
          let orbOpacity = baseOpacity;
          let topValue = `calc(${orb.top} + ${verticalDrift}px)`;

          // Animate based on section state
          if (state === "above") {
            // Slide out: left orbs to left, right orbs to right
            if (orb.side === "left") {
              pos = -orb.size - 400;
            } else {
              const width = window.innerWidth;
              pos = width + orb.size + 400;
            }
            orbOpacity = 0;
          } else if (state === "in") {
            // in view: normal
          } else if (state === "below") {
            // Slide out: left orbs to left, right orbs to right
            if (orb.side === "left") {
              pos = -orb.size - 400;
            } else {
              const width = window.innerWidth;
              pos = width + orb.size + 400;
            }
            orbOpacity = 0;
          }

          return (
            <div
              key={id + "-" + i}
              className="absolute rounded-full"
              style={{
                width: orb.size,
                height: orb.size,
                top: topValue,
                left: orb.side === "left" ? `${pos}px` : undefined,
                right: orb.side === "right" ? `${pos}px` : undefined,
                background: `radial-gradient(circle, ${orb.color} 0%, transparent 70%)`,
                filter: "blur(60px)",
                opacity: orbOpacity,
                transition,
                pointerEvents: "none",
              }}
            />
          );
        });
      })}
    </div>
  );
} 