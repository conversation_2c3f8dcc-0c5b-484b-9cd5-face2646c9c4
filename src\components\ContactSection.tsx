'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaGithub, FaLinkedin } from './icons';
import emailjs from '@emailjs/browser';

const ContactSection = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [toast, setToast] = useState<{ type: 'success' | 'error', msg: string } | null>(null);

  // Hide toast after 3 seconds
  React.useEffect(() => {
    if (toast) {
      const timer = setTimeout(() => setToast(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [toast]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  const formFieldVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const buttonVariants = {
    idle: {
      scale: 1,
      backgroundColor: "rgba(124, 58, 237, 1)"
    },
    loading: {
      scale: 1.02,
      backgroundColor: "rgba(124, 58, 237, 0.8)"
    },
    success: {
      scale: 1,
      backgroundColor: "rgba(34, 197, 94, 1)"
    }
  };

  const socialCardVariants = {
    hidden: { y: 20, opacity: 0, scale: 0.9 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "backOut"
      }
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const isValidEmail = (email: string) => /.+@.+\..+/.test(email);
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isValidEmail(formData.email)) {
      setToast({ type: 'error', msg: 'Please enter a valid email address.' });
      return;
    }
    setIsSubmitting(true);
    try {
      await emailjs.send(
        'service_sxkg0c8',
        'template_8uf6tkv',
        {
          name: formData.name,
          user_email: formData.email,
          message: formData.message,
          time: new Date().toLocaleString(),
          reply_to: formData.email,
        },
        'QbiMivJJwMgyBsr18'
      );
      setIsSubmitted(true);
      setToast({ type: 'success', msg: 'Message sent successfully!' });
      setFormData({ name: '', email: '', message: '' });
      setTimeout(() => setIsSubmitted(false), 3000);
    } catch (err) {
      setToast({ type: 'error', msg: 'Failed to send message. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFocus = (fieldName: string) => {
    setFocusedField(fieldName);
  };

  const handleBlur = () => {
    setFocusedField(null);
  };

  const contactInfo = [
    {
      icon: '📧',
      label: 'Email',
      value: '<EMAIL>',
      link: 'mailto:<EMAIL>'
    },
    {
      icon: '📱',
      label: 'Phone',
      value: '9054714583',
      link: 'tel:9054714583'
    },
    {
      icon: '📍',
      label: 'Location',
      value: 'Vadodara, Gujarat',
      link: '#'
    }
  ];

  const socialLinks = [
    {
      icon: 'github',
      label: 'GitHub',
      url: 'https://github.com/Jugalsoni18',
      color: 'from-gray-700 to-gray-800'
    },
    {
      icon: 'linkedin',
      label: 'LinkedIn',
      url: 'https://www.linkedin.com/in/jugal-soni-8bb797308?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app',
      color: 'from-blue-600 to-blue-700'
    },
    {
      icon: '🐦',
      label: 'Twitter',
      url: 'https://twitter.com/jugalsoni',
      color: 'from-blue-400 to-blue-500'
    },
    {
      icon: '📷',
      label: 'Instagram',
      url: 'https://instagram.com/jugalsoni',
      color: 'from-pink-500 to-purple-600'
    }
  ];

  return (
    <section id="contact-section" className="min-h-screen py-20 relative">{/* Background removed - using unified background */}

      <div className="w-full max-w-[90rem] mx-auto px-8 relative z-8">
        <motion.div
          className="w-full"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: false, amount: 0.3 }}
        >
          {/* Section Header */}
          <motion.div className="text-center mb-16" variants={itemVariants}>
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="text-white-smoke" style={{fontSize: "6.5rem"}}>Get In </span>
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-royal-purple to-electric-indigo" style={{fontSize: "6.5rem"}}>
                Touch
              </span>
            </h2>
            <p className="text-2xl text-gray-400 max-w-4xl mx-auto">
              Have a project in mind or want to collaborate? I'd love to hear from you. Let's create something amazing together!
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <motion.div className="space-y-12 flex flex-col justify-center w-full" variants={itemVariants}>
              <div className="glass p-20 rounded-2xl w-full drop-shadow-[0_0_60px_rgba(124,58,237,0.25)]">
                <h3 className="text-4xl font-bold text-white-smoke mb-8 flex items-center max-w-8xl">
                  Send me a message <span className="ml-2">💌</span>
                </h3>
                <form onSubmit={handleSubmit} className="space-y-8">
                  <div>
                    <label htmlFor="name" className="block text-xl font-medium text-gray-300 mb-3">
                      Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full px-6 py-4 bg-deep-violet/20 border border-royal-purple/30 rounded-lg text-white-smoke placeholder-gray-400 focus:outline-none focus:border-electric-indigo focus:ring-2 focus:ring-electric-indigo transition-colors text-xl "
                      placeholder="Your name"
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-xl font-medium text-gray-300 mb-3">
                      Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-6 py-4 bg-deep-violet/20 border border-royal-purple/30 rounded-lg text-white-smoke placeholder-gray-400 focus:outline-none focus:border-electric-indigo focus:ring-2 focus:ring-electric-indigo transition-colors text-xl"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="message" className="block text-xl font-medium text-gray-300 mb-3">
                      Message
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      rows={7}
                      className="w-full px-6 py-4 bg-deep-violet/20 border border-royal-purple/30 rounded-lg text-white-smoke placeholder-gray-400 focus:outline-none focus:border-electric-indigo focus:ring-2 focus:ring-electric-indigo transition-colors resize-none text-xl min-h-[180px]"
                      placeholder="Your Message..."
                      required
                    />
                  </div>
                  <motion.button
                    type="submit"
                    disabled={isSubmitting || isSubmitted}
                    className={`w-full px-10 py-6 text-2xl font-semibold rounded-lg transition-all duration-300 relative overflow-hidden ${
                      isSubmitted
                        ? 'bg-green-600 hover:bg-green-700'
                        : 'bg-gradient-to-r from-royal-purple to-electric-indigo hover:shadow-lg hover:shadow-royal-purple/25'
                    }`}
                    variants={buttonVariants}
                    animate={
                      isSubmitted ? 'success' :
                      isSubmitting ? 'loading' : 'idle'
                    }
                    whileHover={!isSubmitting && !isSubmitted ? { scale: 1.02 } : {}}
                    whileTap={!isSubmitting && !isSubmitted ? { scale: 0.98 } : {}}
                  >
                    <motion.div
                      className="flex items-center justify-center gap-2"
                      animate={{
                        opacity: isSubmitting ? 0.7 : 1
                      }}
                    >
                      {isSubmitting && (
                        <motion.div
                          className="w-6 h-6 border-2 border-white border-t-transparent rounded-full"
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        />
                      )}
                      {isSubmitted && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ duration: 0.5, ease: "backOut" }}
                        >
                          ✓
                        </motion.div>
                      )}
                      <span>
                        {isSubmitted ? 'Message Sent!' :
                         isSubmitting ? 'Sending...' : 'Send Message'}
                      </span>
                    </motion.div>
                    {/* Success confetti effect remains unchanged */}
                    {isSubmitted && (
                      <motion.div
                        className="absolute inset-0 pointer-events-none"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                      >
                        {[...Array(6)].map((_, i) => (
                          <motion.div
                            key={i}
                            className="absolute w-2 h-2 bg-white rounded-full"
                            initial={{
                              x: '50%',
                              y: '50%',
                              scale: 0
                            }}
                            animate={{
                              x: `${50 + (Math.random() - 0.5) * 200}%`,
                              y: `${50 + (Math.random() - 0.5) * 200}%`,
                              scale: [0, 1, 0],
                              opacity: [0, 1, 0]
                            }}
                            transition={{
                              duration: 1.5,
                              delay: i * 0.1,
                              ease: "easeOut"
                            }}
                          />
                        ))}
                      </motion.div>
                    )}
                  </motion.button>
                </form>
              </div>
            </motion.div>

            {/* Contact Info & Social */}
            <motion.div className="space-y-6 flex flex-col items-stretch w-full" variants={itemVariants}>
              {/* Contact Information */}
              <div className="glass p-20 rounded-2xl w-full drop-shadow-[0_0_60px_rgba(124,58,237,0.25)]">
                <h3 className="text-4xl font-bold text-white-smoke mb-6 items-left">
                  Contact Information
                </h3>
                
                <div className="space-y-4 text-2xl items-left">
                  {contactInfo.map((info, index) => (
                    <motion.a
                      key={index}
                      href={info.link}
                      className="flex items-center space-x-4 p-4 rounded-lg hover:bg-white/5 transition-colors group text-2xl"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="text-2xl">{info.icon}</div>
                      <div>
                        <div className="text-2xl text-gray-400">{info.label}</div>
                        <div className="text-white-smoke group-hover:text-electric-indigo transition-colors">
                          {info.value}
                        </div>
                      </div>
                    </motion.a>
                  ))}
                </div>
              </div>

              {/* Social Links */}
              {/* Removed the 'Follow Me' section as requested */}

              {/* Quick Response */}
              <div className="glass p-10 rounded-xl text-2xl w-full drop-shadow-[0_0_60px_rgba(124,58,237,0.25)]">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-white-smoke font-semibold">Quick Response</span>
                </div>
                <p className="text-gray-300 text-lg">
                  I typically respond to messages within 24 hours. For urgent inquiries, 
                  feel free to reach out via phone or Whatsapp.
                </p>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>

      {/* Footer */}
      <motion.footer 
        className="mt-20 py-0 border-t border-royal-purple/20"
        variants={itemVariants}
      >
        <div className="container mx-auto px-6 text-center">
          <p className="text-2xl mt-10 text-gray-400">
            © 2025 Jugal Soni. All rights reserved.
          </p>
        </div>
      </motion.footer>

      {/* Toast Notification */}
      {toast && (
        <motion.div
          className={`fixed bottom-8 left-1/2 -translate-x-1/2 z-[99999] px-6 py-4 rounded-xl font-semibold text-white shadow-lg ${toast.type === 'success' ? 'bg-green-600' : 'bg-red-600'}`}
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 40 }}
          transition={{ duration: 0.4 }}
        >
          {toast.msg}
        </motion.div>
      )}
    </section>
  );
};

export default ContactSection;
