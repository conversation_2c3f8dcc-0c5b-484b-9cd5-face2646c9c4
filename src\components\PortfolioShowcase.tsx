'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useInView, useAnimation, useScroll, useTransform } from 'framer-motion';
import DirectionalScrollAnimation from './DirectionalScrollAnimation';
import { FaGithub } from './icons';
import { projects, Project } from '../data/projects';
import Link from 'next/link';
import { FaTimes } from 'react-icons/fa';

const PortfolioShowcase = () => {
  const [activeTab, setActiveTab] = useState('projects');
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [100, -100]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);

  const isInView = useInView(sectionRef, {
    once: false,
    amount: 0.3,
    margin: "-100px 0px -100px 0px"
  });

  useEffect(() => {
    if (isInView) {
      setIsVisible(true);
    } else {
      // Reset to false when out of view to allow re-triggering
      setIsVisible(false);
    }
  }, [isInView]);

  // --- Add filter/tabs for All | AI | Web | Backend | Automation ---
  const projectCategories = [
    { id: 'all', label: 'All' },
    { id: 'AI', label: 'AI' },
    { id: 'Web', label: 'Web' },
    { id: 'Backend', label: 'Backend' },
    { id: 'Automation', label: 'Automation' }
  ];
  const [selectedCategory, setSelectedCategory] = useState('all');
  const filteredProjects = selectedCategory === 'all'
    ? projects
    : projects.filter((p: Project) => p.category.includes(selectedCategory));

  const certificates = [
    {
      title: "React Developer Certification",
      issuer: "Meta",
      date: "2023",
      image: "/images/react-cert.png", // Example image in public/images/
      link: "#"
    },
    {
      title: "IBM PY0101EN Certificate",
      issuer: "SRM University",
      date: "2023",
      image: "/images/ibm-py0101en-certificate.png",
      link: "#"
    }
  ];

  // Tech stack data - flat structure like eki.my.id
  const techStack = [
    { name: "HTML", icon: "🌐", color: "#E34F26", bgGradient: "from-orange-500/20 to-red-500/20" },
    { name: "CSS", icon: "🎨", color: "#1572B6", bgGradient: "from-blue-500/20 to-blue-600/20" },
    { name: "JavaScript", icon: "⚡", color: "#F7DF1E", bgGradient: "from-yellow-400/20 to-yellow-500/20" },
    { name: "Tailwind CSS", icon: "🌊", color: "#06B6D4", bgGradient: "from-cyan-400/20 to-cyan-500/20" },
    { name: "ReactJS", icon: "⚛️", color: "#61DAFB", bgGradient: "from-cyan-300/20 to-blue-400/20" },
    { name: "Razorpay API", icon: "💳", color: "#646CFF", bgGradient: "from-purple-400/20 to-indigo-500/20" },
    { name: "Node.JS", icon: "🟢", color: "#339933", bgGradient: "from-green-500/20 to-green-600/20" },
    { name: "PostgreSQL", icon: "🐘", color: "#7952B3", bgGradient: "from-purple-600/20 to-purple-700/20" },
    { name: "Firebase", icon: "🔥", color: "#FFCA28", bgGradient: "from-orange-400/20 to-yellow-500/20" },
    { name: "Next.js", icon: "⏭️", color: "#0081CB", bgGradient: "from-blue-600/20 to-blue-700/20" },
    { name: "Python", icon: "🐍", color: "#3085D6", bgGradient: "from-blue-500/20 to-indigo-600/20" },
    { name: "Express.js", icon: "🚂", color: "#3085D6", bgGradient: "from-blue-500/20 to-indigo-600/20" },
  ];

  // Enhanced animation variants inspired by eki.my.id
  const containerVariants = {
    hidden: {
      opacity: 0,
      y: 40,
      scale: 0.98
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 1.6,
        ease: [0.25, 0.1, 0.25, 1],
        delayChildren: 0.4,
        staggerChildren: 0.2
      }
    }
  };

  const headerVariants = {
    hidden: {
      opacity: 0,
      y: -50,
      scale: 0.9
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 1,
        ease: [0.25, 0.46, 0.45, 0.94],
        delay: 0.2
      }
    }
  };

  const tabVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94],
        delay: 0.4
      }
    }
  };

  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 50,
      scale: 0.95,
      rotateX: 8
    },
    visible: (index: number) => ({
      opacity: 1,
      y: 0,
      scale: 1,
      rotateX: 0,
      transition: {
        duration: 1.4,
        ease: [0.25, 0.1, 0.25, 1],
        delay: index * 0.15
      }
    }),
    hover: {
      y: -15,
      scale: 1.03,
      rotateX: 8,
      rotateY: 8,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  const tabContentVariants = {
    hidden: {
      opacity: 0,
      x: 50,
      scale: 0.95,
      filter: "blur(10px)"
    },
    visible: {
      opacity: 1,
      x: 0,
      scale: 1,
      filter: "blur(0px)",
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94],
        delayChildren: 0.2,
        staggerChildren: 0.1
      }
    },
    exit: {
      opacity: 0,
      x: -50,
      scale: 0.95,
      filter: "blur(10px)",
      transition: {
        duration: 0.5,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  const techStackVariants = {
    hidden: {
      opacity: 0,
      scale: 0.5,
      rotate: -180
    },
    visible: (index: number) => ({
      opacity: 1,
      scale: 1,
      rotate: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94],
        delay: index * 0.05
      }
    }),
    hover: {
      scale: 1.1,
      rotate: 5,
      y: -8,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  // --- Restore the main tab bar (Projects | Certificates | Tech Stack) ---
  const mainTabs = [
    { id: 'projects', label: 'Projects', icon: <FaGithub className="w-8 h-8" /> },
    { id: 'certificates', label: 'Certificates', icon: <span className="text-4xl mt-1">🏆</span> },
    { id: 'techstack', label: 'Tech Stack', icon: <span className="text-4xl mt-1">⚡</span> }
  ];

  const [openCert, setOpenCert] = useState<string | null>(null);

  // ESC to close modal (no body blur)
  useEffect(() => {
    if (!openCert) return;
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') setOpenCert(null);
    };
    window.addEventListener('keydown', handleEsc);
    return () => window.removeEventListener('keydown', handleEsc);
  }, [openCert]);

  return (
    <>
      {/* Main content with conditional blur */}
      <div className={openCert ? 'filter blur-[8px] pointer-events-none select-none' : ''}>
        <motion.section
          ref={sectionRef}
          id="portfolio-showcase"
          className="eki-section relative overflow-hidden"
          style={{ y, opacity }}
        >
          {/* Enhanced Background Effects */}
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-royal-purple/[0.03] to-transparent"></div>

          {/* Floating particles effect */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-electric-indigo/20 rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [0, -100, 0],
                  opacity: [0, 1, 0],
                  scale: [0, 1, 0]
                }}
                transition={{
                  duration: 8 + (i % 4),
                  repeat: Infinity,
                  delay: i * 0.3,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>

          <div className="relative z-10 max-w-[140rem] mx-auto px-6">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate={isVisible ? "visible" : "hidden"}
            >
              {/* Enhanced Section Header */}
              <motion.div
                className="text-center mb-16"
                variants={headerVariants}
              >
                <motion.h2
                  className="text-4xl md:text-6xl lg:text-8xl font-bold mb-6"
                  initial={{ opacity: 0, y: 50 }}
                  animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
                  transition={{ duration: 1, delay: 0.3 }}
                >
                  <span className="text-white-smoke ">Portfolio </span>
                  <motion.span
                    className="gradient-text "
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={isVisible ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.8, delay: 0.6, }}
                  >
                    Showcase
                  </motion.span>
                </motion.h2>
                <motion.p
                  className="text-xl text-text-muted max-w-3xl mx-auto leading-relaxed "
                  initial={{ opacity: 0, y: 30 }}
                  animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                  transition={{ duration: 0.8, delay: 0.8 }}
                >
                  Explore my journey through projects, certifications, and technical expertise
                </motion.p>
              </motion.div>

              {/* Main Tab Navigation */}
              <motion.div
                className="relative flex gap-8 bg-black/30 backdrop-blur-lg border border-white/10 rounded-2xl mb-12 py-4 "
                variants={tabVariants}
              >
                {mainTabs.map((tab, index) => {
                  const isProjects = tab.id === 'projects';
                  return (
                    <motion.button
                      key={tab.id}
                      className={`eki-tab ${activeTab === tab.id ? 'active' : ''}${isProjects ? ' flex items-center justify-center' : ''}`}
                      onClick={() => setActiveTab(tab.id)}
                      whileHover={{
                        scale: 1.05,
                        y: -2,
                        transition: { duration: 0.2 }
                      }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, y: 20 }}
                      animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                      transition={{ duration: 0.6, delay: 1 + (index * 0.1) }}
                    >
                      {isProjects ? (
                        <>
                          <span>{tab.icon}</span>
                          <span className="!text-4xl ml-2">{tab.label}</span>
                        </>
                      ) : (
                        <>
                          <span>{tab.icon}</span>
                          <span className="!text-4xl">{tab.label}</span>
                        </>
                      )}
                    </motion.button>
                  );
                })}
              </motion.div>

              {/* Enhanced Tab Content */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  variants={tabContentVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                >
                  {activeTab === 'projects' && (
                    <motion.div
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                    >
                      {/* Enhanced grid for larger project cards - EKI Style */}
                      <div className="grid grid-cols-1 xl:grid-cols-3 gap-12 px-4 mx-auto max-w-9xl ">
                        {projects.map((project: Project, index: number) => (
                          <motion.div
                            key={project.id}
                            className="relative bg-black/30 backdrop-blur-xl border-2 border-white/20 rounded-3xl p-8 group overflow-hidden min-h-[500px] flex-shrink-0 w-full shadow-2xl hover:shadow-purple-500/20 transition-all duration-500"
                            custom={index}
                            variants={cardVariants}
                            whileHover="hover"
                            style={{
                              transformStyle: "preserve-3d"
                            }}
                          >
                            {/* Enhanced Card glow effect */}
                            <div className="absolute inset-0 bg-gradient-to-r from-royal-purple/30 to-electric-indigo/30 rounded-3xl opacity-0 group-hover:opacity-60 transition-opacity duration-700 blur-lg" />
                            {/* Additional subtle glow layers */}
                            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-blue-500/10 rounded-3xl opacity-0 group-hover:opacity-40 transition-opacity duration-500 blur" />
                            <div className="absolute inset-0 bg-gradient-to-tl from-pink-500/5 to-purple-500/5 rounded-3xl opacity-0 group-hover:opacity-30 transition-opacity duration-500 blur-sm" />
                            <div className="relative z-10 h-full flex flex-col">
                              {/* Enhanced Project Screenshot/Preview */}
                              <div className="w-full flex justify-center mb-8">
                                <div className="relative w-full h-80 bg-gradient-to-br from-[#18122b] to-[#2a1f4b] rounded-2xl p-4 shadow-2xl border border-white/10 overflow-hidden">
                                  <img
                                    src={project.screenshot}
                                    alt={`Screenshot of ${project.title}`}
                                    className="rounded-xl shadow-xl object-contain w-full h-full bg-transparent hover:scale-105 transition-transform duration-500"
                                    style={{ background: 'transparent' }}
                                  />
                                  {/* Subtle overlay effect */}
                                  <div className="absolute inset-0 bg-gradient-to-t from-transparent via-transparent to-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                                </div>
                              </div>
                              {/* Enhanced Title */}
                              <motion.h3
                                className="text-3xl md:text-4xl font-bold mb-4 text-white-smoke group-hover:text-electric-indigo transition-colors duration-300 leading-tight"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: index * 0.1 + 0.3 }}
                              >
                                {project.title}
                              </motion.h3>
                              {/* Enhanced Description */}
                              <motion.p
                                className="text-lg md:text-xl text-text-muted mb-8 leading-relaxed flex-grow"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: index * 0.1 + 0.4 }}
                              >
                                {project.cardSummary}
                              </motion.p>
                              {/* Enhanced Buttons */}
                              <motion.div
                                className="flex gap-6 mt-auto"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: index * 0.1 + 0.7 }}
                              >
                                <Link 
                                  href={`/projects/${project.id}`} 
                                  className="eki-button text-lg md:text-xl px-8 py-4 bg-gradient-to-r from-royal-purple/90 to-electric-indigo/90 hover:from-royal-purple hover:to-electric-indigo rounded-xl font-semibold shadow-lg hover:shadow-purple-500/30 transition-all duration-300 flex items-center gap-3"
                                >
                                  <span>View Project</span>
                                  <span className="text-xl">→</span>
                                </Link>
                                <motion.a
                                  href={project.github}
                                  className="eki-button text-lg md:text-xl px-8 py-4 bg-gray-800/80 hover:bg-gray-700/90 rounded-xl font-semibold border border-gray-600/50 hover:border-gray-500/70 shadow-lg hover:shadow-gray-500/20 transition-all duration-300 flex items-center gap-3"
                                  whileHover={{
                                    scale: 1.02,
                                    boxShadow: "0 0 25px rgba(75, 85, 99, 0.4)"
                                  }}
                                  whileTap={{ scale: 0.98 }}
                                >
                                  <span>GitHub</span>
                                  <span className="text-xl">↗</span>
                                </motion.a>
                              </motion.div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>
                  )}
                  {activeTab === 'certificates' && (
                    <>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 w-full">
                        {certificates.map((cert, index) => (
                          cert.image.endsWith('.png') ? (
                            <div
                              key={index}
                              className="relative group h-[440px] w-full border-4 border-[#e0e0e0] shadow-2xl bg-white/90 cursor-pointer"
                            >
                              <img
                                src={cert.image}
                                alt={cert.title}
                                className="h-full w-full object-contain"
                              />
                              <button
                                className="absolute inset-0 flex items-center justify-center bg-black/40 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white font-bold text-xl z-10"
                                onClick={() => setOpenCert(cert.image)}
                                aria-label="View Certificate"
                                tabIndex={0}
                              >
                                View Certificate
                              </button>
                            </div>
                          ) : null
                        ))}
                      </div>
                    </>
                  )}
                  {activeTab === 'techstack' && (
                    <motion.div
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                      className="w-full"
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-x-1 gap-y-8 w-full justify-items-center ">
                        {techStack.map((tech, index) => (
                          <motion.div
                            key={index}
                            className="group aspect-square w-64 h-64 md:w-64 md:h-64 flex flex-col items-center justify-center bg-black/40 backdrop-blur-md border border-white/10 rounded-2xl shadow-lg transition-all duration-300 hover:shadow-[0_0_24px_#a855f7,0_0_48px_#6366f1] hover:border-electric-indigo/60 cursor-pointer relative"
                            whileHover={{ y: -8, scale: 1.06 }}
                            whileTap={{ scale: 0.97 }}
                          >
                            {/* Subtle always-on glow */}
                            <div className="pointer-events-none absolute inset-0 rounded-2xl z-0" style={{ boxShadow: '0 0 32px 4px #7c3aed22, 0 0 64px 8px #655dbb22' }} />
                            <span className="text-9xl mb-10 z-10" style={{ color: tech.color }}>{tech.icon}</span>
                            <span className="text-white-smoke font-bold text-xl text-center tracking-wide select-none z-10">
                              {tech.name}
                            </span>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              </AnimatePresence>
            </motion.div>
          </div>
        </motion.section>
      </div>
      {/* Certificate Modal */}
      <AnimatePresence>
        {openCert && (
          <motion.div
            className="fixed inset-0 z-[9999] flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            aria-modal="true"
            role="dialog"
            tabIndex={-1}
            onClick={() => setOpenCert(null)}
          >
            {/* Backdrop */}
            <div className="absolute inset-0 bg-black/60 cursor-pointer" />
            {/* Modal content */}
            <motion.div
              className="relative flex flex-col items-center justify-center p-4 bg-transparent"
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              transition={{ duration: 0.3, ease: 'easeOut' }}
              onClick={e => e.stopPropagation()}
            >
              <img
                src={openCert}
                alt="Certificate Preview"
                className="max-w-[90vw] h-auto max-h-[80vh] shadow-2xl rounded-xl bg-white object-contain"
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default PortfolioShowcase;