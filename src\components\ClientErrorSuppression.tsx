'use client';

import { useEffect } from 'react';

const ClientErrorSuppression = () => {
  useEffect(() => {
    // Store original console methods
    const originalError = console.error;
    const originalWarn = console.warn;

    // Override console.error to filter Spline-related errors
    console.error = (...args: any[]) => {
      const message = args.join(' ');
      
      // Filter out common Spline errors that don't affect functionality
      const splineErrorPatterns = [
        'spline',
        'splinetool',
        'runtime',
        'buildTimeline',
        'console.error',
        'handleConsoleError',
        'createConsoleError',
        'Missing property',
        'eval',
        'mg.buildTimeline',
        'use-error-handler',
        'intercept-console-error'
      ];

      const shouldSuppress = splineErrorPatterns.some(pattern => 
        message.toLowerCase().includes(pattern.toLowerCase())
      );

      if (!shouldSuppress) {
        originalError.apply(console, args);
      }
    };

    // Override console.warn to filter Spline-related warnings
    console.warn = (...args: any[]) => {
      const message = args.join(' ');
      
      const splineWarningPatterns = [
        'spline',
        'splinetool',
        'runtime',
        'scene failed to load'
      ];

      const shouldSuppress = splineWarningPatterns.some(pattern => 
        message.toLowerCase().includes(pattern.toLowerCase())
      );

      if (!shouldSuppress) {
        originalWarn.apply(console, args);
      }
    };

    // Cleanup function to restore original console methods
    return () => {
      console.error = originalError;
      console.warn = originalWarn;
    };
  }, []);

  return null; // This component doesn't render anything
};

export default ClientErrorSuppression;
