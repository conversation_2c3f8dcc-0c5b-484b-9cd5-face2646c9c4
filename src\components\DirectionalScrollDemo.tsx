'use client';

import React from 'react';
import DirectionalScrollAnimation from './DirectionalScrollAnimation';

const DirectionalScrollDemo = () => {
  return (
    <div className="min-h-screen bg-primary-bg text-text-primary p-8">
      <div className="max-w-4xl mx-auto space-y-16">
        
        {/* Header */}
        <DirectionalScrollAnimation animation="fadeIn" duration={1}>
          <div className="text-center">
            <h1 className="text-5xl font-bold gradient-text mb-4">
              Directional Scroll Animations
            </h1>
            <p className="text-xl text-text-secondary">
              Animations that only trigger when scrolling downward
            </p>
          </div>
        </DirectionalScrollAnimation>

        {/* Fade In Animation */}
        <DirectionalScrollAnimation animation="fadeIn" duration={0.8} delay={0.2}>
          <div className="eki-card p-8">
            <h2 className="text-3xl font-bold text-accent-purple mb-4">Fade In Animation</h2>
            <p className="text-text-secondary">
              This element fades in smoothly when you scroll down to it. 
              It won't animate when scrolling back up.
            </p>
          </div>
        </DirectionalScrollAnimation>

        {/* Slide Up Animation */}
        <DirectionalScrollAnimation animation="slideUp" duration={0.8} delay={0.1}>
          <div className="eki-card p-8">
            <h2 className="text-3xl font-bold text-sky-blue mb-4">Slide Up Animation</h2>
            <p className="text-text-secondary">
              This card slides up from below with a smooth easing effect.
              Perfect for content reveals.
            </p>
          </div>
        </DirectionalScrollAnimation>

        {/* Slide Left Animation */}
        <DirectionalScrollAnimation animation="slideLeft" duration={0.8} delay={0.3}>
          <div className="eki-card p-8">
            <h2 className="text-3xl font-bold text-neon-violet mb-4">Slide Left Animation</h2>
            <p className="text-text-secondary">
              This element slides in from the right side when scrolling down.
              Great for alternating content layouts.
            </p>
          </div>
        </DirectionalScrollAnimation>

        {/* Slide Right Animation */}
        <DirectionalScrollAnimation animation="slideRight" duration={0.8} delay={0.2}>
          <div className="eki-card p-8">
            <h2 className="text-3xl font-bold text-gradient-end mb-4">Slide Right Animation</h2>
            <p className="text-text-secondary">
              This card slides in from the left side with a custom delay.
              Perfect for creating dynamic layouts.
            </p>
          </div>
        </DirectionalScrollAnimation>

        {/* Scale Animation */}
        <DirectionalScrollAnimation animation="scale" duration={0.8} delay={0.1}>
          <div className="eki-card p-8">
            <h2 className="text-3xl font-bold text-accent-purple mb-4">Scale Animation</h2>
            <p className="text-text-secondary">
              This element scales up from a smaller size when entering the viewport.
              Creates a nice zoom-in effect.
            </p>
          </div>
        </DirectionalScrollAnimation>

        {/* Custom Animation */}
        <DirectionalScrollAnimation 
          animation="custom"
          duration={1}
          delay={0.2}
          customVariants={{
            hidden: { 
              opacity: 0, 
              y: 100, 
              rotateX: -15,
              scale: 0.8
            },
            visible: { 
              opacity: 1, 
              y: 0, 
              rotateX: 0,
              scale: 1
            }
          }}
        >
          <div className="eki-card p-8 bg-gradient-to-br from-accent-purple/20 to-neon-violet/20">
            <h2 className="text-3xl font-bold gradient-text mb-4">Custom Animation</h2>
            <p className="text-text-secondary">
              This element uses a custom animation variant with rotation, scale, and movement.
              You can create any animation combination you want.
            </p>
          </div>
        </DirectionalScrollAnimation>

        {/* Grid of Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((item, index) => (
            <DirectionalScrollAnimation 
              key={item}
              animation="slideUp" 
              duration={0.6} 
              delay={index * 0.1}
            >
              <div className="eki-card p-6 text-center">
                <div className="text-4xl mb-4">🚀</div>
                <h3 className="text-xl font-bold text-accent-purple mb-2">
                  Card {item}
                </h3>
                <p className="text-text-secondary text-sm">
                  Staggered animation with {index * 100}ms delay
                </p>
              </div>
            </DirectionalScrollAnimation>
          ))}
        </div>

        {/* Performance Note */}
        <DirectionalScrollAnimation animation="fadeIn" duration={0.8}>
          <div className="eki-card p-8 border border-accent-purple/30">
            <h2 className="text-3xl font-bold text-accent-purple mb-4">Performance Features</h2>
            <ul className="space-y-2 text-text-secondary">
              <li>✅ Only animates on downward scroll</li>
              <li>✅ Triggers once per element (configurable)</li>
              <li>✅ Uses Intersection Observer for efficiency</li>
              <li>✅ Debounced scroll direction detection</li>
              <li>✅ Passive event listeners</li>
              <li>✅ RequestAnimationFrame optimization</li>
            </ul>
          </div>
        </DirectionalScrollAnimation>

        {/* Configuration Options */}
        <DirectionalScrollAnimation animation="slideUp" duration={0.8}>
          <div className="eki-card p-8">
            <h2 className="text-3xl font-bold text-sky-blue mb-4">Configuration Options</h2>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-bold text-accent-purple mb-2">Animation Types:</h4>
                <ul className="space-y-1 text-text-secondary">
                  <li>• fadeIn</li>
                  <li>• slideUp</li>
                  <li>• slideLeft</li>
                  <li>• slideRight</li>
                  <li>• scale</li>
                  <li>• custom</li>
                </ul>
              </div>
              <div>
                <h4 className="font-bold text-accent-purple mb-2">Options:</h4>
                <ul className="space-y-1 text-text-secondary">
                  <li>• duration: Animation duration</li>
                  <li>• delay: Animation delay</li>
                  <li>• threshold: Intersection threshold</li>
                  <li>• triggerOnce: Animate only once</li>
                  <li>• requireDownwardScroll: Direction requirement</li>
                </ul>
              </div>
            </div>
          </div>
        </DirectionalScrollAnimation>

        {/* Footer */}
        <DirectionalScrollAnimation animation="fadeIn" duration={0.8}>
          <div className="text-center py-16">
            <h3 className="text-2xl font-bold gradient-text mb-4">
              Scroll back up and down to test!
            </h3>
            <p className="text-text-secondary">
              Notice how animations only trigger when scrolling downward
            </p>
          </div>
        </DirectionalScrollAnimation>

      </div>
    </div>
  );
};

export default DirectionalScrollDemo;
